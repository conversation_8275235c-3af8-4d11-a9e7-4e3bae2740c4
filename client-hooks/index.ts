// client-hooks/index.ts

import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// Common API types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  filter?: Record<string, any>;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

interface UuidParams {
  id: string;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Define API types based on your backend schemas
interface FaqItem {
  id: string;
  questions: string[];
  answer: string;
  botId: string;
  flowId?: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateFaqItemRequest {
  botId: string;
  flowId?: string;
  questions: string[];
  answer: string;
  category: string;
}

interface UpdateFaqItemRequest {
  botId?: string;
  flowId?: string;
  questions?: string[];
  answer?: string;
  category?: string;
}

interface IntentItem {
  id: string;
  botId: string;
  flowId?: string;
  name: string;
  examples: Array<{ id?: string; name: string; value: string }>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateIntentItemRequest {
  botId: string;
  flowId?: string;
  name: string;
  examples?: Array<{ id?: string; name: string; value: string }>;
}

interface UpdateIntentItemRequest {
  botId?: string;
  flowId?: string;
  name?: string;
  examples?: Array<{ id?: string; name: string; value: string }>;
}

interface Entity {
  id: string;
  name: string;
  botId: string;
  intentId?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateEntityRequest {
  botId: string;
  intentId?: string;
  name: string;
}

interface UpdateEntityRequest {
  botId?: string;
  intentId?: string;
  name?: string;
}

// Define a base API slice (assuming it's not defined elsewhere)
const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({ baseUrl: "/api/v1" }), // Adjust base URL as needed
  endpoints: (builder) => ({}),
});

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ["FaqItem", "IntentItem", "Entity"],
});

export const knowledgeApi = updatedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // FAQ Items
    getFaqItems: builder.query<ApiResponse<PaginatedResponse<FaqItem>>, PaginationParams>({
      query: (params) => ({
        url: "/faq-items",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["FaqItem"],
    }),
    getFaqItem: builder.query<ApiResponse<FaqItem>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-items/${id}` }),
      providesTags: ["FaqItem"],
    }),
    createFaqItem: builder.mutation<ApiResponse<FaqItem>, CreateFaqItemRequest>({
      query: (body) => ({
        url: "/faq-items",
        method: "POST",
        body,
      }),
      invalidatesTags: ["FaqItem"],
    }),
    updateFaqItem: builder.mutation<ApiResponse<FaqItem>, UuidParams & UpdateFaqItemRequest>({
      query: ({ id, ...body }) => ({
        url: `/faq-items/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["FaqItem"],
    }),
    deleteFaqItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["FaqItem"],
    }),

    // Intent Items
    getIntentItems: builder.query<ApiResponse<PaginatedResponse<IntentItem>>, PaginationParams>({
      query: (params) => ({
        url: "/intent-items",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["IntentItem"],
    }),
    getIntentItem: builder.query<ApiResponse<IntentItem>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-items/${id}` }),
      providesTags: ["IntentItem"],
    }),
    createIntentItem: builder.mutation<ApiResponse<IntentItem>, CreateIntentItemRequest>({
      query: (body) => ({
        url: "/intent-items",
        method: "POST",
        body,
      }),
      invalidatesTags: ["IntentItem"],
    }),
    updateIntentItem: builder.mutation<
      ApiResponse<IntentItem>,
      UuidParams & UpdateIntentItemRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-items/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["IntentItem"],
    }),
    deleteIntentItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["IntentItem"],
    }),

    // Entities
    getEntities: builder.query<ApiResponse<PaginatedResponse<Entity>>, PaginationParams>({
      query: (params) => ({
        url: "/entities",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["Entity"],
    }),
    getEntity: builder.query<ApiResponse<Entity>, UuidParams>({
      query: ({ id }) => ({ url: `/entities/${id}` }),
      providesTags: ["Entity"],
    }),
    createEntity: builder.mutation<ApiResponse<Entity>, CreateEntityRequest>({
      query: (body) => ({
        url: "/entities",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Entity"],
    }),
    updateEntity: builder.mutation<ApiResponse<Entity>, UuidParams & UpdateEntityRequest>({
      query: ({ id, ...body }) => ({
        url: `/entities/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Entity"],
    }),
    deleteEntity: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/entities/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Entity"],
    }),
  }),
});

export const {
  useGetFaqItemsQuery,
  useGetFaqItemQuery,
  useCreateFaqItemMutation,
  useUpdateFaqItemMutation,
  useDeleteFaqItemMutation,
  useGetIntentItemsQuery,
  useGetIntentItemQuery,
  useCreateIntentItemMutation,
  useUpdateIntentItemMutation,
  useDeleteIntentItemMutation,
  useGetEntitiesQuery,
  useGetEntityQuery,
  useCreateEntityMutation,
  useUpdateEntityMutation,
  useDeleteEntityMutation,
} = knowledgeApi;
