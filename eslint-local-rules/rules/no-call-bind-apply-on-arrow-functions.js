module.exports = {
  meta: {
    type: "problem",
    docs: {
      description: "Disallow the use of .call(), .bind(), and .apply() on arrow functions.",
      category: "Best Practices",
      recommended: true,
    },
    schema: [],
    messages: {
      noBindOnArrow: "Do not use .bind() on an arrow function.",
      noCallOnArrow: "Do not use .call() on an arrow function.",
      noApplyOnArrow: "Do not use .apply() on an arrow function.",
    },
  },
  create(context) {
    return {
      CallExpression(node) {
        const callee = node.callee;

        if (callee.type !== "MemberExpression") {
          return;
        }

        const object = callee.object;
        const property = callee.property;

        if (object.type === "ArrowFunctionExpression") {
          if (property.name === "bind") {
            context.report({
              node: property,
              messageId: "noBindOnArrow",
            });
          } else if (property.name === "call") {
            context.report({
              node: property,
              messageId: "noCallOnArrow",
            });
          } else if (property.name === "apply") {
            context.report({
              node: property,
              messageId: "noApplyOnArrow",
            });
          }
        }
      },
    };
  },
};
