module.exports = {
  meta: {
    type: "problem",
    docs: {
      description: "Public methods in controller files must be arrow functions.",
    },
    schema: [],
    messages: {
      mustBeArrow: "Public methods in controller files must be arrow functions.",
    },
  },
  create(context) {
    return {
      MethodDefinition(node) {
        // Skip constructors
        if (node.kind === "constructor") {
          return;
        }

        const filename = context.getFilename();
        const isController = filename.endsWith(".controller.ts");
        const isPublic = node.accessibility === "public" || node.accessibility === undefined;
        const isFunction = node.value?.type === "FunctionExpression";

        if (isController && isPublic && isFunction) {
          context.report({
            node,
            messageId: "mustBeArrow",
          });
        }
      },
    };
  },
};
