module.exports = {
  rules: {
    "enforce-arrow-functions-in-controllers": require("./rules/enforce-arrow-functions-in-controllers"),
    "no-call-bind-apply-on-arrow-functions": require("./rules/no-call-bind-apply-on-arrow-functions"),
  },
  configs: {
    recommended: {
      plugins: ["local-rules"],
      rules: {
        "local-rules/enforce-arrow-functions-in-controllers": "error",
        "local-rules/no-call-bind-apply-on-arrow-functions": "error",
      },
    },
  },
};
