/**
 * Chat Controller
 *
 * Handles HTTP requests for chat logging and conversation history.
 */

import { Request, Response } from "express";
import { ChatService } from "../services/chat.service";
import {
  LogMessageRequest,
  LogMessageResponse,
  GetConversationHistoryResponse,
  GetConversationsResponse,
  ApiResponse,
} from "@neuratalk/common";
import logger from "../utils/logger";

export class ChatController {
  private chatService: ChatService;

  constructor(chatService: ChatService) {
    this.chatService = chatService;
  }

  /**
   * @swagger
   * /api/internal/v1/log-message:
   *   post:
   *     summary: Log a message to the database
   *     description: Stores a message in the conversation history (internal endpoint)
   *     tags: [Internal, Messages]
   *     security:
   *       - ApiKeyAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/LogMessageRequest'
   *           example:
   *             message:
   *               id: "msg-123e4567-e89b-12d3-a456-426614174000"
   *               conversationId: "conv-123e4567-e89b-12d3-a456-426614174000"
   *               sender: "user"
   *               content: "Hello, I need help with my order"
   *               messageType: "text"
   *               timestamp: "2023-12-01T10:30:00Z"
   *               metadata:
   *                 userId: "user123"
   *                 channel: "web"
   *             context:
   *               userId: "user123"
   *               botId: "bot-123e4567-e89b-12d3-a456-426614174000"
   *     responses:
   *       200:
   *         description: Message logged successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/LogMessageResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       401:
   *         description: Unauthorized - invalid API key
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  async logMessage(req: Request, res: Response): Promise<void> {
    try {
      const request: LogMessageRequest = req.body;

      if (!request.message) {
        res.status(400).json({
          success: false,
          error: {
            code: "MISSING_MESSAGE",
            message: "Message is required",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      const messageId = await this.chatService.logMessage(request);

      const response: LogMessageResponse = {
        messageId,
        logged: true,
      };

      res.json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<LogMessageResponse>);
    } catch (error) {
      logger.error("Error in logMessage controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to log message",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * GET /api/v1/conversations/{id}/history
   * Get conversation history
   */
  async getConversationHistory(req: Request, res: Response): Promise<void> {
    try {
      const { id: conversationId } = req.params;
      const {
        page = 1,
        limit = 50,
        fromDate,
        toDate,
        messageTypes,
      } = req.query as any;

      const { messages, total } = await this.chatService.getConversationHistory(
        conversationId,
        parseInt(page, 10),
        parseInt(limit, 10),
        fromDate ? new Date(fromDate) : undefined,
        toDate ? new Date(toDate) : undefined,
        messageTypes ? messageTypes.split(",") : undefined
      );

      const totalPages = Math.ceil(total / limit);

      const response: GetConversationHistoryResponse = {
        items: messages,
        pagination: {
          page: parseInt(page, 10),
          limit: parseInt(limit, 10),
          total,
          totalPages,
          hasNext: parseInt(page, 10) < totalPages,
          hasPrev: parseInt(page, 10) > 1,
        },
      };

      res.json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<GetConversationHistoryResponse>);
    } catch (error) {
      logger.error("Error in getConversationHistory controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to get conversation history",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * GET /api/v1/conversations
   * Get conversations with pagination and filtering
   */
  async getConversations(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        userId,
        botId,
        status,
        fromDate,
        toDate,
      } = req.query as any;

      const { conversations, total } = await this.chatService.getConversations(
        parseInt(page, 10),
        parseInt(limit, 10),
        userId,
        botId,
        status,
        fromDate ? new Date(fromDate) : undefined,
        toDate ? new Date(toDate) : undefined
      );

      const totalPages = Math.ceil(total / limit);

      const response: GetConversationsResponse = {
        items: conversations,
        pagination: {
          page: parseInt(page, 10),
          limit: parseInt(limit, 10),
          total,
          totalPages,
          hasNext: parseInt(page, 10) < totalPages,
          hasPrev: parseInt(page, 10) > 1,
        },
      };

      res.json({
        success: true,
        data: response,
        timestamp: new Date(),
      } as ApiResponse<GetConversationsResponse>);
    } catch (error) {
      logger.error("Error in getConversations controller:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Failed to get conversations",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * GET /health
   * Health check endpoint
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const healthResult = await this.chatService.healthCheck();

      const statusCode = healthResult.status === "healthy" ? 200 : 503;

      res.status(statusCode).json({
        success: healthResult.status === "healthy",
        data: {
          status: healthResult.status,
          service: "chat-service",
          version: process.env.npm_package_version || "1.0.0",
          responseTime: healthResult.responseTime,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error in health check:", error);
      res.status(503).json({
        success: false,
        error: {
          code: "HEALTH_CHECK_ERROR",
          message: "Health check failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
