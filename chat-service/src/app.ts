/**
 * Chat Service Application
 *
 * Main application setup for the chat-service.
 */

import express, { Application, Request, Response, NextFunction } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";

import { Sequelize } from "sequelize";
import { initModels, Models } from "./models";
import { ChatService } from "./services/chat.service";
import { ChatController } from "./controllers/chat.controller";
import { authMiddleware } from "./middleware/auth.middleware";

import config from "./config";
import logger from "./utils/logger";
import { ApiResponse } from "@neuratalk/common";

export class App {
  public app: Application;
  private sequelize!: Sequelize;
  private models!: Models;
  private chatService!: ChatService;
  private chatController!: ChatController;

  constructor() {
    this.app = express();
    this.initializeServices();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeServices(): void {
    this.sequelize = new Sequelize(config.database.url, {
      dialect: "postgres",
      logging: config.server.env === "development" ? console.log : false,
    });

    this.models = initModels(this.sequelize);
    this.chatService = new ChatService();
    this.chatController = new ChatController(this.chatService);
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // CORS configuration
    this.app.use(
      cors({
        origin: config.server.corsOrigins,
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization", "X-API-Key"],
      })
    );

    // Compression
    this.app.use(compression() as any);

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        })
      );
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);
      next();
    });
  }

  private initializeRoutes(): void {
    // Swagger documentation
    this.app.get("/api-docs", (req: Request, res: Response) => {
      res.send(
        swaggerUi.generateHTML(swaggerSpec, {
          explorer: true,
          customCss: ".swagger-ui .topbar { display: none }",
          customSiteTitle: "Chat Service API Documentation",
        })
      );
    });

    // Swagger JSON endpoint
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Health check (no authentication required)
    this.app.get("/health", (req, res) =>
      this.chatController.healthCheck(req, res)
    );

    // Internal API routes (require authentication)
    this.app.post("/api/internal/v1/log-message", authMiddleware, (req, res) =>
      this.chatController.logMessage(req, res)
    );

    // Public API routes (for now, no authentication - in production, add proper auth)
    this.app.get("/api/v1/conversations/:id/history", (req, res) =>
      this.chatController.getConversationHistory(req, res)
    );

    this.app.get("/api/v1/conversations", (req, res) =>
      this.chatController.getConversations(req, res)
    );

    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          service: "chat-service",
          version: process.env.npm_package_version || "1.0.0",
          environment: config.server.env,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // 404 handler
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use(
      (error: Error, req: Request, res: Response, next: NextFunction) => {
        logger.error("Unhandled error:", {
          error: error.message,
          stack: error.stack,
          requestId: (req as any).requestId,
          method: req.method,
          path: req.path,
        });

        const message =
          config.server.env === "production"
            ? "An internal error occurred"
            : error.message;

        res.status(500).json({
          success: false,
          error: {
            code: "INTERNAL_ERROR",
            message,
            ...(config.server.env !== "production" && { stack: error.stack }),
          },
          timestamp: new Date(),
        } as ApiResponse);
      }
    );
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await this.sequelize.authenticate();

      // Sync models in development
      if (config.server.env === "development") {
        await this.sequelize.sync({ alter: true });
      }

      // Start server
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`Chat Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Chat Service...");
      await this.sequelize.close();
      logger.info("Chat Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
