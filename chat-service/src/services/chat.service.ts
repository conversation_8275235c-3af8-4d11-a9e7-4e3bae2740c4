/**
 * Chat Service
 *
 * Handles conversation and message persistence operations.
 */

import {
  Message,
  Conversation,
  ConversationSummary,
  LogMessageRequest,
  GetConversationHistoryRequest,
  GetConversationsRequest,
} from "@neuratalk/common";
import { Pool } from "pg";
import { v4 as uuidv4 } from "uuid";
import config from "../config";
import logger from "../utils/logger";

export class ChatService {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      connectionString: config.database.url,
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.maxConnections,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.pool.on("connect", () => {
      logger.debug("Database client connected");
    });

    this.pool.on("error", (err) => {
      logger.error("Database pool error:", err);
    });
  }

  async connect(): Promise<void> {
    try {
      // Test the connection
      const client = await this.pool.connect();
      await client.query("SELECT NOW()");
      client.release();

      logger.info("Chat service database connection established");
    } catch (error) {
      logger.error("Failed to connect to database:", error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.pool.end();
      logger.info("Chat service database connection closed");
    } catch (error) {
      logger.error("Error closing database connection:", error);
    }
  }

  /**
   * Log a message to the database
   */
  async logMessage(request: LogMessageRequest): Promise<string> {
    const client = await this.pool.connect();

    try {
      await client.query("BEGIN");

      // Ensure conversation exists
      await this.ensureConversationExists(
        client,
        request.message.conversationId,
        request.context
      );

      // Insert message
      const messageQuery = `
        INSERT INTO messages (id, conversation_id, sender, content, message_type, timestamp, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;

      const messageValues = [
        request.message.id,
        request.message.conversationId,
        request.message.sender,
        request.message.content,
        request.message.messageType,
        request.message.timestamp,
        JSON.stringify(request.message.metadata || {}),
      ];

      const messageResult = await client.query(messageQuery, messageValues);
      const messageId = messageResult.rows[0].id;

      // Update conversation last_message_at and increment message count
      const updateConversationQuery = `
        UPDATE conversations 
        SET last_message_at = $1, message_count = message_count + 1
        WHERE id = $2
      `;

      await client.query(updateConversationQuery, [
        request.message.timestamp,
        request.message.conversationId,
      ]);

      await client.query("COMMIT");

      logger.debug(
        `Message logged: ${messageId} for conversation ${request.message.conversationId}`
      );
      return messageId;
    } catch (error) {
      await client.query("ROLLBACK");
      logger.error("Error logging message:", error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get conversation history with pagination
   */
  async getConversationHistory(
    conversationId: string,
    page: number = 1,
    limit: number = 50,
    fromDate?: Date,
    toDate?: Date,
    messageTypes?: string[]
  ): Promise<{ messages: Message[]; total: number }> {
    try {
      const offset = (page - 1) * limit;

      const conditions: string[] = ["conversation_id = $1"];
      const values: any[] = [conversationId];
      let paramIndex = 2;

      if (fromDate) {
        conditions.push(`timestamp >= $${paramIndex++}`);
        values.push(fromDate);
      }

      if (toDate) {
        conditions.push(`timestamp <= $${paramIndex++}`);
        values.push(toDate);
      }

      if (messageTypes && messageTypes.length > 0) {
        conditions.push(`message_type = ANY($${paramIndex++})`);
        values.push(messageTypes);
      }

      const whereClause = `WHERE ${conditions.join(" AND ")}`;

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM messages ${whereClause}`;
      const countResult = await this.pool.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count, 10);

      // Get messages
      values.push(limit, offset);
      const query = `
        SELECT * FROM messages 
        ${whereClause}
        ORDER BY timestamp ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const result = await this.pool.query(query, values);

      const messages: Message[] = result.rows.map((row: any) => ({
        id: row.id,
        conversationId: row.conversation_id,
        sender: row.sender,
        content: row.content,
        messageType: row.message_type,
        timestamp: row.timestamp,
        metadata: row.metadata,
      }));

      return { messages, total };
    } catch (error) {
      logger.error(
        `Error getting conversation history for ${conversationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get conversations with pagination and filtering
   */
  async getConversations(
    page: number = 1,
    limit: number = 20,
    userId?: string,
    botId?: string,
    status?: string,
    fromDate?: Date,
    toDate?: Date
  ): Promise<{ conversations: ConversationSummary[]; total: number }> {
    try {
      const offset = (page - 1) * limit;

      const conditions: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (userId) {
        conditions.push(`user_id = $${paramIndex++}`);
        values.push(userId);
      }

      if (botId) {
        conditions.push(`bot_id = $${paramIndex++}`);
        values.push(botId);
      }

      if (status) {
        conditions.push(`status = $${paramIndex++}`);
        values.push(status);
      }

      if (fromDate) {
        conditions.push(`started_at >= $${paramIndex++}`);
        values.push(fromDate);
      }

      if (toDate) {
        conditions.push(`started_at <= $${paramIndex++}`);
        values.push(toDate);
      }

      const whereClause =
        conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM conversations ${whereClause}`;
      const countResult = await this.pool.query(countQuery, values);
      const total = parseInt(countResult.rows[0].count, 10);

      // Get conversations with last message
      values.push(limit, offset);
      const query = `
        SELECT 
          c.*,
          m.content as last_message
        FROM conversations c
        LEFT JOIN LATERAL (
          SELECT content 
          FROM messages 
          WHERE conversation_id = c.id 
          ORDER BY timestamp DESC 
          LIMIT 1
        ) m ON true
        ${whereClause}
        ORDER BY c.last_message_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const result = await this.pool.query(query, values);

      const conversations: ConversationSummary[] = result.rows.map(
        (row: any) => ({
          id: row.id,
          userId: row.user_id,
          botId: row.bot_id,
          status: row.status,
          startedAt: row.started_at,
          lastMessageAt: row.last_message_at,
          messageCount: row.message_count,
          lastMessage: row.last_message,
        })
      );

      return { conversations, total };
    } catch (error) {
      logger.error("Error getting conversations:", error);
      throw error;
    }
  }

  /**
   * Ensure conversation exists in database
   */
  private async ensureConversationExists(
    client: any,
    conversationId: string,
    context?: any
  ): Promise<void> {
    try {
      // Check if conversation exists
      const checkQuery = "SELECT id FROM conversations WHERE id = $1";
      const checkResult = await client.query(checkQuery, [conversationId]);

      if (checkResult.rows.length === 0) {
        // Create conversation
        const insertQuery = `
          INSERT INTO conversations (id, user_id, bot_id, status, started_at, last_message_at, message_count, metadata)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `;

        const values = [
          conversationId,
          context?.userId || null,
          context?.botId || null,
          "active",
          new Date(),
          new Date(),
          0,
          JSON.stringify({}),
        ];

        await client.query(insertQuery, values);
        logger.debug(`Conversation created: ${conversationId}`);
      }
    } catch (error) {
      logger.error(
        `Error ensuring conversation exists: ${conversationId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    responseTime?: number;
  }> {
    const start = Date.now();

    try {
      const client = await this.pool.connect();
      await client.query("SELECT 1");
      client.release();

      const responseTime = Date.now() - start;

      return {
        status: "healthy",
        responseTime,
      };
    } catch (error) {
      logger.error("Chat service health check failed:", error);
      return {
        status: "unhealthy",
      };
    }
  }
}
