/**
 * Authentication Middleware
 *
 * Handles API key authentication for internal requests.
 */

import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "@neuratalk/common";
import config from "../config";
import logger from "../utils/logger";

/**
 * Middleware for API key authentication
 */
export function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const apiKey = req.headers["x-api-key"] as string;

    if (!apiKey) {
      res.status(401).json({
        success: false,
        error: {
          code: "MISSING_API_KEY",
          message: "API key is required",
        },
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    if (apiKey !== config.security.apiKey) {
      logger.warn(`Invalid API key attempt from ${req.ip}`);
      res.status(401).json({
        success: false,
        error: {
          code: "INVALID_API_KEY",
          message: "Invalid API key",
        },
        timestamp: new Date(),
      } as ApiResponse);
      return;
    }

    next();
  } catch (error) {
    logger.error("Error in auth middleware:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "AUTH_ERROR",
        message: "Authentication failed",
      },
      timestamp: new Date(),
    } as ApiResponse);
  }
}
