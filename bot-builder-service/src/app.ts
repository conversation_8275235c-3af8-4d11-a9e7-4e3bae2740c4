/**
 * Bot Builder Service Application
 *
 * Main application setup for the bot-builder-service.
 */

import express, { Application, Request, Response, NextFunction } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";

import config from "./config";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { ApiResponse, logger } from "@neuratalk/common";
import { AppContext } from "./types/context.types";
import { createRoutes } from "./routers/index.router";
import { BotService } from "./services/bot.service";
import { FlowService } from "./services/flow.service";
import { RasaService } from "./services/rasa.service";
import { authMiddleware } from "./middleware/auth.middleware";

export class App {
  public app: Application;
  private context!: AppContext;

  constructor() {
    this.app = express();
  }

  private async initializeCoreServices(): Promise<void> {
    const db = new DatabaseConnection();
    await db.connect();

    const rasaService = new RasaService(db.models);
    const botService = new BotService(db.models, rasaService, db);
    const flowService = new FlowService(db.models);

    this.context = {
      db,
      rasaService,
      botService,
      flowService,
    };

    logger.info("Core services initialized.");
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    //TODO: enable it after deployment
    // CORS configuration
    // this.app.use(
    //   cors({
    //     origin: config.server.corsOrigins,
    //     credentials: true,
    //     methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    //     allowedHeaders: ["Content-Type", "Authorization"],
    //   })
    // );
    this.app.use(cors());

    // Compression
    this.app.use(compression());

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        }),
      );
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);
      next();
    });
  }

  private initializeRoutes(): void {
    // Swagger documentation
    this.app.use(
      "/api-docs",
      ...swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        swaggerOptions: {
          persistAuthorization: true,
          docExpansion: "list",
        },
      }),
    );

    // Swagger JSON endpoint
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Health check
    this.app.get("/health", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          status: "healthy",
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    //TODO: need to rm authMiddleware from here if not required for all routes
    const routers = createRoutes(this.context);
    routers.forEach(router => {
      this.app.use("/api/v1", authMiddleware, router);
    });

    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          environment: config.server.env,
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler (must be last route)
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error("Unhandled error:", {
        error: error.message,
        stack: error.stack,
        requestId: (req as any).requestId,
        method: req.method,
        path: req.path,
      });

      const message =
        config.server.env === "production" ? "An internal error occurred" : error.message;

      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message,
          ...(config.server.env !== "production" && { stack: error.stack }),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  public async start(): Promise<void> {
    try {
      // Initialize services, controllers, and routes
      await this.initializeCoreServices();
      this.initializeMiddleware();
      this.initializeRoutes();
      this.initializeErrorHandling();

      // Start server
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`Bot Builder Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Bot Builder Service...");
      await this.context.db.disconnect();
      logger.info("Bot Builder Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
