import { Router } from "express";
import { FaqItemsController } from "../controllers/faq-items.controller";
import { IntentItemsController } from "../controllers/intent-items.controller";
import { EntitiesController } from "../controllers/entities.controller";
import { LanguageController } from "../controllers/language.controller";
import { BotLanguageController } from "../controllers/bot-language.controller";
import { FaqCategoryController } from "../controllers/faq-category.controller";
import { FaqTranslationController } from "../controllers/faq-translation.controller";
import { IntentUtteranceController } from "../controllers/intent-utterance.controller";
import { IntentUtteranceTranslationController } from "../controllers/intent-utterance-translation.controller";

import { authMiddleware } from "../middleware/auth.middleware";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateEntitySchema,
  UpdateEntitySchema,
  CreateLanguageSchema,
  UpdateLanguageSchema,
  CreateBotLanguageSchema,
  UpdateBotLanguageSchema,
  CreateFaqCategorySchema,
  UpdateFaqCategorySchema,
  CreateFaqTranslationSchema,
  UpdateFaqTranslationSchema,
  CreateIntentUtteranceSchema,
  UpdateIntentUtteranceSchema,
  CreateIntentUtteranceTranslationSchema,
  UpdateIntentUtteranceTranslationSchema,
} from "../schemas";

import { AppContext } from "../types/context.types";

export function createKnowledgeRoutes(context: AppContext): Router {
  const router = Router();

  const faqItemsController = new FaqItemsController(context);
  const intentItemsController = new IntentItemsController(context);
  const entitiesController = new EntitiesController(context);
  const languageController = new LanguageController(context);
  const botLanguageController = new BotLanguageController(context);
  const faqCategoryController = new FaqCategoryController(context);
  const faqTranslationController = new FaqTranslationController(context);
  const intentUtteranceController = new IntentUtteranceController(context);
  const intentUtteranceTranslationController = new IntentUtteranceTranslationController(context);

  

  // FAQ Category routes
  router.post(
    "/faq-categories",
    authMiddleware,
    validateBody(CreateFaqCategorySchema),
    faqCategoryController.create.bind(faqCategoryController),
  );
  router.get("/faq-categories", validateQuery(PaginationQuerySchema), faqCategoryController.getAll);
  router.get(
    "/faq-categories/:id",
    validateParams(UuidParamSchema),
    faqCategoryController.getById.bind(faqCategoryController),
  );
  router.put(
    "/faq-categories/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqCategorySchema),
    faqCategoryController.update.bind(faqCategoryController),
  );
  router.delete(
    "/faq-categories/:id",
    validateParams(UuidParamSchema),
    faqCategoryController.delete.bind(faqCategoryController),
  );

  // FAQ Items routes
  router.post(
    "/faq-items",
    authMiddleware,
    validateBody(CreateFaqItemSchema),
    faqItemsController.create.bind(faqItemsController),
  );
  router.get("/faq-items", validateQuery(PaginationQuerySchema), faqItemsController.getAll);
  router.get(
    "/faq-items/:id",
    validateParams(UuidParamSchema),
    faqItemsController.getById.bind(faqItemsController),
  );
  router.put(
    "/faq-items/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqItemSchema),
    faqItemsController.update.bind(faqItemsController),
  );
  router.delete(
    "/faq-items/:id",
    validateParams(UuidParamSchema),
    faqItemsController.delete.bind(faqItemsController),
  );

  // FAQ Translation routes
  router.post(
    "/faq-translations",
    authMiddleware,
    validateBody(CreateFaqTranslationSchema),
    faqTranslationController.create.bind(faqTranslationController),
  );
  router.get("/faq-translations", validateQuery(PaginationQuerySchema), faqTranslationController.getAll);
  router.get(
    "/faq-translations/:id",
    validateParams(UuidParamSchema),
    faqTranslationController.getById.bind(faqTranslationController),
  );
  router.put(
    "/faq-translations/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqTranslationSchema),
    faqTranslationController.update.bind(faqTranslationController),
  );
  router.delete(
    "/faq-translations/:id",
    validateParams(UuidParamSchema),
    faqTranslationController.delete.bind(faqTranslationController),
  );

  // Intent Items routes
  router.post(
    "/intent-items",
    authMiddleware,
    validateBody(CreateIntentItemSchema),
    intentItemsController.create.bind(intentItemsController),
  );
  router.get(
    "/intent-items",
    validateQuery(PaginationQuerySchema),
    intentItemsController.getAll.bind(intentItemsController),
  );
  router.get(
    "/intent-items/:id",
    validateParams(UuidParamSchema),
    intentItemsController.getById.bind(intentItemsController),
  );
  router.put(
    "/intent-items/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentItemSchema),
    intentItemsController.update.bind(intentItemsController),
  );
  router.delete(
    "/intent-items/:id",
    validateParams(UuidParamSchema),
    intentItemsController.delete.bind(intentItemsController),
  );

  // Intent Utterance routes
  router.post(
    "/intent-utterances",
    authMiddleware,
    validateBody(CreateIntentUtteranceSchema),
    intentUtteranceController.create.bind(intentUtteranceController),
  );
  router.get("/intent-utterances", validateQuery(PaginationQuerySchema), intentUtteranceController.getAll);
  router.get(
    "/intent-utterances/:id",
    validateParams(UuidParamSchema),
    intentUtteranceController.getById.bind(intentUtteranceController),
  );
  router.put(
    "/intent-utterances/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentUtteranceSchema),
    intentUtteranceController.update.bind(intentUtteranceController),
  );
  router.delete(
    "/intent-utterances/:id",
    validateParams(UuidParamSchema),
    intentUtteranceController.delete.bind(intentUtteranceController),
  );

  // Intent Utterance Translation routes
  router.post(
    "/intent-utterance-translations",
    authMiddleware,
    validateBody(CreateIntentUtteranceTranslationSchema),
    intentUtteranceTranslationController.create.bind(intentUtteranceTranslationController),
  );
  router.get("/intent-utterance-translations", validateQuery(PaginationQuerySchema), intentUtteranceTranslationController.getAll);
  router.get(
    "/intent-utterance-translations/:id",
    validateParams(UuidParamSchema),
    intentUtteranceTranslationController.getById.bind(intentUtteranceTranslationController),
  );
  router.put(
    "/intent-utterance-translations/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentUtteranceTranslationSchema),
    intentUtteranceTranslationController.update.bind(intentUtteranceTranslationController),
  );
  router.delete(
    "/intent-utterance-translations/:id",
    validateParams(UuidParamSchema),
    intentUtteranceTranslationController.delete.bind(intentUtteranceTranslationController),
  );

  // Entities routes
  router.post(
    "/entities",
    authMiddleware,
    validateBody(CreateEntitySchema),
    entitiesController.create.bind(entitiesController),
  );
  router.get(
    "/entities",
    validateQuery(PaginationQuerySchema),
    entitiesController.getAll.bind(entitiesController),
  );
  router.get(
    "/entities/:id",
    validateParams(UuidParamSchema),
    entitiesController.getById.bind(entitiesController),
  );
  router.put(
    "/entities/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateEntitySchema),
    entitiesController.update.bind(entitiesController),
  );
  router.delete(
    "/entities/:id",
    validateParams(UuidParamSchema),
    entitiesController.delete.bind(entitiesController),
  );

  return router;
}
