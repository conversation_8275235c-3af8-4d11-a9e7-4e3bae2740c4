import { Router } from "express";
import { LanguageController } from "../controllers/language.controller";
import { BotLanguageController } from "../controllers/bot-language.controller";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateLanguageSchema,
  UpdateLanguageSchema,
  CreateBotLanguageSchema,
  UpdateBotLanguageSchema,
} from "../schemas/bot-language.schemas";
import { authMiddleware } from "../middleware/auth.middleware";
import { AppContext } from "../types/context.types";

export function createLanguageRoutes(context: AppContext): Router {
  const router = Router();

  const languageController = new LanguageController(context);
  const botLanguageController = new BotLanguageController(context);

  // Language routes
  router.post(
    "/languages",
    authMiddleware,
    validateBody(CreateLanguageSchema),
    languageController.create.bind(languageController),
  );
  router.get("/languages", validateQuery(PaginationQuerySchema), languageController.getAll);
  router.get(
    "/languages/:id",
    validateParams(UuidParamSchema),
    languageController.getById.bind(languageController),
  );
  router.put(
    "/languages/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateLanguageSchema),
    languageController.update.bind(languageController),
  );
  router.delete(
    "/languages/:id",
    validateParams(UuidParamSchema),
    languageController.delete.bind(languageController),
  );

  // Bot Language routes
  router.post(
    "/bot-languages",
    authMiddleware,
    validateBody(CreateBotLanguageSchema),
    botLanguageController.create.bind(botLanguageController),
  );
  router.get("/bot-languages", validateQuery(PaginationQuerySchema), botLanguageController.getAll);
  router.get(
    "/bot-languages/:id",
    validateParams(UuidParamSchema),
    botLanguageController.getById.bind(botLanguageController),
  );
  router.put(
    "/bot-languages/:id",
    authMiddleware,
    validateParams(UuidParamSchema),
    validateBody(UpdateBotLanguageSchema),
    botLanguageController.update.bind(botLanguageController),
  );
  router.delete(
    "/bot-languages/:id",
    validateParams(UuidParamSchema),
    botLanguageController.delete.bind(botLanguageController),
  );

  return router;
}
