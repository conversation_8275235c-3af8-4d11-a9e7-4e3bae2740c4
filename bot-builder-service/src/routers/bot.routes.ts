import { Router } from "express";
import { AppContext } from "../types/context.types";
import { BotController } from "../controllers/bot.controller";
import {
  validateBody,
  validateParams,
  validateQuery,
  PaginationQuerySchema,
} from "@neuratalk/common";
import {
  CreateBotRequestSchema,
  UpdateBotRequestSchema,
  BotIdParamSchema,
  BotChannelParamSchema,
  CreateChannelIntegrationSchema,
  BotChannelIdParamSchema,
  UpdateChannelIntegrationSchema,
} from "../schemas";

export function createBotRoutes(context: AppContext): Router {
  const router = Router();
  const botController = new BotController(context);

  router.post("/bots", validateBody(CreateBotRequestSchema), botController.createBot);
  router.get("/bots/:id", validateParams(BotIdParamSchema), botController.getBotById);
  router.put(
    "/bots/:id",
    validateParams(BotIdParamSchema),
    validateBody(UpdateBotRequestSchema),
    botController.updateBot,
  );
  router.delete("/bots/:id", validateParams(BotIdParamSchema), botController.deleteBot);
  router.get("/bots", validateQuery(PaginationQuerySchema), botController.getBots);
  router.post("/bots/:id/activate", validateParams(BotIdParamSchema), botController.activateBot);
  router.post(
    "/bots/:id/deactivate",
    validateParams(BotIdParamSchema),
    botController.deactivateBot,
  );
  router.post("/bots/:id/build", validateParams(BotIdParamSchema), botController.buildBot);

  // Channel routes
  router.get(
    "/bots/:botId/channels/:channelType",
    validateParams(BotChannelParamSchema),
    botController.getChannelConfig,
  );
  router.post(
    "/bots/:botId/channels",
    validateParams(BotIdParamSchema),
    validateBody(CreateChannelIntegrationSchema),
    botController.createChannelIntegration,
  );
  router.put(
    "/bots/:botId/channels/:channelId",
    validateParams(BotChannelIdParamSchema),
    validateBody(UpdateChannelIntegrationSchema),
    botController.updateChannelIntegration,
  );

  return router;
}
