/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";
import { OpenApiGeneratorV3 } from "@asteasolutions/zod-to-openapi";

import {
  AppIdParamSchema,
  CreateAppSchema,
  UpdateAppSchema,
  GetAppsQuerySchema,
  CreateBotRequestSchema,
  UpdateBotRequestSchema,
  BotIdParamSchema,
  BotChannelParamSchema,
  CreateChannelIntegrationSchema,
  BotChannelIdParamSchema,
  UpdateChannelIntegrationSchema,
  CreateFlowRequestSchema,
  FlowIdParamSchema,
  UpdateFlowRequestSchema,
  FlowAppIdParamSchema,
  GetFlowsQuerySchema,
  BulkCreateFlowsRequestSchema,
  CreateFaqCategorySchema,
  UpdateFaqCategorySchema,
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateFaqTranslationSchema,
  UpdateFaqTranslationSchema,
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateIntentUtteranceSchema,
  UpdateIntentUtteranceSchema,
  CreateIntentUtteranceTranslationSchema,
  UpdateIntentUtteranceTranslationSchema,
  CreateEntitySchema,
  UpdateEntitySchema,
  CreateLanguageSchema,
  UpdateLanguageSchema,
  CreateBotLanguageSchema,
  UpdateBotLanguageSchema,
  // Response schemas - using model schemas for consistency
  ApiResponseSchema,
  ErrorResponseSchema,
  ValidationErrorResponseSchema,
  NotFoundErrorResponseSchema,
  InternalServerErrorResponseSchema,
  DeletedResponseSchema,
  // Model schemas
  LanguageSchema,
  BotLanguageSchema,
  EntitySchema,
  FaqCategorySchema,
  FaqItemSchema,
  FaqTranslationSchema,
  IntentItemSchema,
  IntentUtteranceSchema,
  IntentUtteranceTranslationSchema,
  PaginationSchema,
  PaginatedLanguagesSchema,
  PaginatedBotLanguagesSchema,
  PaginatedEntitiesSchema,
  PaginatedFaqCategoriesSchema,
  PaginatedFaqItemsSchema,
  PaginatedFaqTranslationsSchema,
  PaginatedIntentItemsSchema,
  PaginatedIntentUtterancesSchema,
  PaginatedIntentUtteranceTranslationsSchema,
} from "../schemas";

const openApiGenerator = new OpenApiGeneratorV3([
  {
    schema: CreateAppSchema,
    type: "schema",
  },
  {
    schema: UpdateAppSchema,
    type: "schema",
  },
  {
    schema: AppIdParamSchema,
    type: "schema",
  },
  {
    schema: GetAppsQuerySchema,
    type: "schema",
  },
  {
    schema: CreateBotRequestSchema,
    type: "schema",
  },
  {
    schema: UpdateBotRequestSchema,
    type: "schema",
  },
  {
    schema: BotIdParamSchema,
    type: "schema",
  },
  {
    schema: BotChannelParamSchema,
    type: "schema",
  },
  {
    schema: CreateChannelIntegrationSchema,
    type: "schema",
  },
  {
    schema: BotChannelIdParamSchema,
    type: "schema",
  },
  {
    schema: UpdateChannelIntegrationSchema,
    type: "schema",
  },
  {
    schema: CreateFlowRequestSchema,
    type: "schema",
  },
  {
    schema: FlowIdParamSchema,
    type: "schema",
  },
  {
    schema: UpdateFlowRequestSchema,
    type: "schema",
  },
  {
    schema: FlowAppIdParamSchema,
    type: "schema",
  },
  {
    schema: GetFlowsQuerySchema,
    type: "schema",
  },
  {
    schema: BulkCreateFlowsRequestSchema,
    type: "schema",
  },
  {
    schema: CreateFaqCategorySchema,
    type: "schema",
  },
  {
    schema: UpdateFaqCategorySchema,
    type: "schema",
  },
  {
    schema: CreateFaqItemSchema,
    type: "schema",
  },
  {
    schema: UpdateFaqItemSchema,
    type: "schema",
  },
  {
    schema: CreateFaqTranslationSchema,
    type: "schema",
  },
  {
    schema: UpdateFaqTranslationSchema,
    type: "schema",
  },
  {
    schema: CreateIntentItemSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentItemSchema,
    type: "schema",
  },
  {
    schema: CreateIntentUtteranceSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentUtteranceSchema,
    type: "schema",
  },
  {
    schema: CreateIntentUtteranceTranslationSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentUtteranceTranslationSchema,
    type: "schema",
  },
  {
    schema: CreateEntitySchema,
    type: "schema",
  },
  {
    schema: UpdateEntitySchema,
    type: "schema",
  },
  {
    schema: CreateLanguageSchema,
    type: "schema",
  },
  {
    schema: UpdateLanguageSchema,
    type: "schema",
  },
  {
    schema: CreateBotLanguageSchema,
    type: "schema",
  },
  {
    schema: UpdateBotLanguageSchema,
    type: "schema",
  },
  // Response schemas
  {
    schema: ApiResponseSchema,
    type: "schema",
  },
  {
    schema: ErrorResponseSchema,
    type: "schema",
  },
  {
    schema: ValidationErrorResponseSchema,
    type: "schema",
  },
  {
    schema: NotFoundErrorResponseSchema,
    type: "schema",
  },
  {
    schema: InternalServerErrorResponseSchema,
    type: "schema",
  },
  {
    schema: DeletedResponseSchema,
    type: "schema",
  },
  // Model schemas
  {
    schema: LanguageSchema,
    type: "schema",
  },
  {
    schema: BotLanguageSchema,
    type: "schema",
  },
  {
    schema: EntitySchema,
    type: "schema",
  },
  {
    schema: FaqCategorySchema,
    type: "schema",
  },
  {
    schema: FaqItemSchema,
    type: "schema",
  },
  {
    schema: FaqTranslationSchema,
    type: "schema",
  },
  {
    schema: IntentItemSchema,
    type: "schema",
  },
  {
    schema: IntentUtteranceSchema,
    type: "schema",
  },
  {
    schema: IntentUtteranceTranslationSchema,
    type: "schema",
  },
  {
    schema: PaginationSchema,
    type: "schema",
  },
  {
    schema: PaginatedLanguagesSchema,
    type: "schema",
  },
  {
    schema: PaginatedBotLanguagesSchema,
    type: "schema",
  },
  {
    schema: PaginatedEntitiesSchema,
    type: "schema",
  },
  {
    schema: PaginatedFaqCategoriesSchema,
    type: "schema",
  },
  {
    schema: PaginatedFaqItemsSchema,
    type: "schema",
  },
  {
    schema: PaginatedFaqTranslationsSchema,
    type: "schema",
  },
  {
    schema: PaginatedIntentItemsSchema,
    type: "schema",
  },
  {
    schema: PaginatedIntentUtterancesSchema,
    type: "schema",
  },
  {
    schema: PaginatedIntentUtteranceTranslationsSchema,
    type: "schema",
  },
]);

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Builder Service API",
    version: "1.0.0",
    description:
      "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Development server",
    },
    {
      url: "https://api.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for user authentication",
      },
    },
    ...openApiGenerator.generateComponents(),
  },
  tags: [
    {
      name: "Apps",
      description: "Application management operations",
    },
    {
      name: "Bots",
      description: "Bot management operations",
    },
    {
      name: "Flows",
      description: "Flow management operations",
    },
    {
      name: "Health",
      description: "Health check endpoints",
    },
    {
      name: "FAQ Categories",
      description: "FAQ category management operations",
    },
    {
      name: "FAQ Items",
      description: "FAQ item management operations",
    },
    {
      name: "FAQ Translations",
      description: "FAQ translation management operations",
    },
    {
      name: "Intent Items",
      description: "Intent item management operations",
    },
    {
      name: "Intent Utterances",
      description: "Intent utterance management operations",
    },
    {
      name: "Intent Utterance Translations",
      description: "Intent utterance translation management operations",
    },
    {
      name: "Entities",
      description: "Entity management operations",
    },
    {
      name: "Languages",
      description: "Language management operations",
    },
    {
      name: "Bot Languages",
      description: "Bot language configuration operations",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: ["./src/controllers/*.ts", "./src/routers/*.ts", "./src/app.ts"],
  swaggerOptions: {
    persistAuthorization: true,
  },
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;
