/**
 * Bot Controller
 *
 * Handles HTTP requests for bot management operations.
 */

import { Request, Response } from "express";
import { AppContext } from "../types/context.types";
import { BotService } from "../services/bot.service";
import { CreateBotRequest, UpdateBotRequest, BuildBotResponse } from "../types";
import {
  getPaginatedResults,
  logger,
  PaginationQuery,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { Models } from "@neuratalk/bot-store";

export class BotController {
  private botService: BotService;
  private models: Models;

  constructor(context: AppContext) {
    this.botService = context.botService;
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bots:
   *   post:
   *     summary: Create a new bot
   *     description: Creates a new chatbot with the specified configuration
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *           example:
   *             name: "Customer Support Bot"
   *             description: "Handles customer inquiries and support requests"
   *     responses:
   *       201:
   *         description: Bot created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       format: uuid
   *                     name:
   *                       type: string
   *                     description:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       400:
   *         description: Invalid request data
   *       500:
   *         description: Internal server error
   */
  public createBot = async (
    req: Request<any, any, CreateBotRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user?.id;

      const bot = await this.botService.createBot(req.body, userId);

      res.status(201).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in createBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to create bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   get:
   *     summary: Get bot by ID
   *     description: Retrieves a specific bot by its unique identifier
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       format: uuid
   *                     name:
   *                       type: string
   *                     description:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  public getBotById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const bot = await this.botService.getBotById(id);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in getBotById controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to get bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   put:
   *     summary: Update bot
   *     description: Updates an existing bot with new information
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *     responses:
   *       200:
   *         description: Bot updated successfully
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  public updateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const request: UpdateBotRequest = req.body;
      const userId = req.user?.id;

      const bot = await this.botService.updateBot(id, request, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in updateBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to update bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   delete:
   *     summary: Delete bot
   *     description: Deletes an existing bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     responses:
   *       204:
   *         description: Bot deleted successfully
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  public deleteBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const deleted = await this.botService.deleteBot(id);

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots:
   *   get:
   *     summary: Get all bots
   *     description: Retrieves a paginated list of all bots
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: Number of items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term for bot names
   *     responses:
   *       200:
   *         description: Bots retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             format: uuid
   *                           name:
   *                             type: string
   *                           description:
   *                             type: string
   *                     page:
   *                       type: integer
   *                     limit:
   *                       type: integer
   *                     total:
   *                       type: integer
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       500:
   *         description: Internal server error
   */
  getBots = async (req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.Bot, req.query as PaginationQuery, [
        "name",
      ]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching Bots:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch Bots" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}/activate:
   *   post:
   *     summary: Activate bot
   *     description: Activates a bot for use
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     responses:
   *       200:
   *         description: Bot activated successfully
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  activateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.activateBot(id, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in activateBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to activate bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}/deactivate:
   *   post:
   *     summary: Deactivate bot
   *     description: Deactivates a bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     responses:
   *       200:
   *         description: Bot deactivated successfully
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  deactivateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = (req as any).user?.id;

      const bot = await this.botService.deactivateBot(id, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in deactivateBot controller:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to deactivate bot" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}/build:
   *   post:
   *     summary: Build a Rasa bot
   *     description: Generates Rasa NLU files for the specified bot in the rasa-nlu directory
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot built successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     success:
   *                       type: boolean
   *                     message:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  buildBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      // Build the bot
      const result = await this.botService.buildBot(id);

      res.json(successResponse<BuildBotResponse>(result));
    } catch (error: any) {
      logger.error("Error in buildBot controller:", error);

      // Handle not found error
      if (error.message && error.message.includes("not found")) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to build bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/channels/{channelType}:
   *   get:
   *     summary: Get channel configuration
   *     description: Retrieves channel configuration for a specific bot and channel type
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *       - in: path
   *         name: channelType
   *         required: true
   *         schema:
   *           type: string
   *           enum: [web, whatsapp, telegram, slack]
   *         description: Channel type
   *     responses:
   *       200:
   *         description: Channel configuration retrieved successfully
   *       404:
   *         description: Bot or channel not found
   *       500:
   *         description: Internal server error
   */
  getChannelConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId, channelType } = req.params;
      const config = await this.botService.getChannelConfig(botId, channelType);

      res.json(successResponse(config));
    } catch (error) {
      logger.error("Error getting channel config:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "GET_CHANNEL_CONFIG_ERROR",
          message: "Failed to get channel configuration",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/clone:
   *   post:
   *     summary: Clone a bot
   *     description: Creates a new bot as a clone of the specified bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot cloned successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       format: uuid
   *                     name:
   *                       type: string
   *                     description:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       404:
   *         description: Bot not found
   *       500:
   *         description: Internal server error
   */
  cloneBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId } = req.params;
      const userId = (req as any).user?.id;
      const bot = await this.botService.cloneBot(botId, userId);

      if (!bot) {
        res.status(404).json(errorResponse({ code: "BOT_NOT_FOUND", message: "Bot not found" }));
        return;
      }

      res.json(successResponse(bot));
    } catch (error) {
      logger.error("Error in cloneBot controller:", error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to clone bot" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/channels:
   *   post:
   *     summary: Create channel integration
   *     description: Creates a new channel integration for a bot
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               channelType:
   *                 type: string
   *                 enum: [web, whatsapp, telegram, slack]
   *               config:
   *                 type: object
   *     responses:
   *       201:
   *         description: Channel integration created successfully
   *       400:
   *         description: Invalid request data
   *       500:
   *         description: Internal server error
   */
  createChannelIntegration = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId } = req.params;
      const channelData = req.body;
      const integration = await this.botService.createChannelIntegration(botId, channelData);

      res.status(201).json(successResponse(integration));
    } catch (error) {
      logger.error("Error creating channel integration:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "CREATE_CHANNEL_ERROR",
          message: "Failed to create channel integration",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{botId}/channels/{channelId}:
   *   put:
   *     summary: Update channel integration
   *     description: Updates an existing channel integration
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *       - in: path
   *         name: channelId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Channel integration unique identifier
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               config:
   *                 type: object
   *     responses:
   *       200:
   *         description: Channel integration updated successfully
   *       404:
   *         description: Bot or channel integration not found
   *       500:
   *         description: Internal server error
   */
  updateChannelIntegration = async (req: Request, res: Response): Promise<void> => {
    try {
      const { botId, channelId } = req.params;
      const updateData = req.body;
      const integration = await this.botService.updateChannelIntegration(
        botId,
        channelId,
        updateData,
      );

      res.json(successResponse(integration));
    } catch (error) {
      logger.error("Error updating channel integration:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "UPDATE_CHANNEL_ERROR",
          message: "Failed to update channel integration",
        }),
      );
    }
  };
}
