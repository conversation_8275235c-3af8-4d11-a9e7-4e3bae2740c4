import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateLanguageRequest, UpdateLanguageRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class LanguageController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/languages:
   *   post:
   *     summary: Create a new language
   *     tags: [Languages]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - code
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Name of the language (e.g., "English")
   *               code:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 10
   *                 description: Language code (e.g., "en", "en-US")
   *     responses:
   *       201:
   *         description: Language created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       format: uuid
   *                     name:
   *                       type: string
   *                     code:
   *                       type: string
   *                     createdAt:
   *                       type: string
   *                       format: date-time
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: false
   *                 error:
   *                   type: object
   *                   properties:
   *                     code:
   *                       type: string
   *                       example: "VALIDATION_ERROR"
   *                     message:
   *                       type: string
   *                     details:
   *                       type: object
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: false
   *                 error:
   *                   type: object
   *                   properties:
   *                     code:
   *                       type: string
   *                       example: "INTERNAL_ERROR"
   *                     message:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   */
  public create = async (
    req: Request<any, any, CreateLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const language = await this.models.Language.create({
        ...req.body,
      });

      logger.info(`Language created: ${language.id}`);
      res.status(201).json(successResponse(language));
    } catch (error) {
      logger.error("Error creating language:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/languages:
   *   get:
   *     summary: Get all languages
   *     tags: [Languages]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: List of languages with pagination
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             format: uuid
   *                           name:
   *                             type: string
   *                           code:
   *                             type: string
   *                           createdAt:
   *                             type: string
   *                             format: date-time
   *                     pagination:
   *                       type: object
   *                       properties:
   *                         page:
   *                           type: integer
   *                         limit:
   *                           type: integer
   *                         total:
   *                           type: integer
   *                         totalPages:
   *                           type: integer
   *                         hasNext:
   *                           type: boolean
   *                         hasPrev:
   *                           type: boolean
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: false
   *                 error:
   *                   type: object
   *                   properties:
   *                     code:
   *                       type: string
   *                       example: "INTERNAL_ERROR"
   *                     message:
   *                       type: string
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.Language, req.query, ["name", "code"]);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching languages:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch languages" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/languages/{id}:
   *   get:
   *     summary: Get language by ID
   *     tags: [Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Language object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const language = await this.models.Language.findOne({
        where: { id } as WhereOptions,
      });

      if (!language) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Language not found" }));
        return;
      }

      res.json(successResponse(language));
    } catch (error) {
      logger.error(`Error fetching language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch language" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/languages/{id}:
   *   put:
   *     summary: Update a language
   *     tags: [Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * name:
                  * type: string
                * code:
                  * type: string
   *     responses:
   *       200:
   *         description: Language updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.Language.update(
        {
          ...req.body,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Language not found" }));
        return;
      }

      const language = await this.models.Language.findByPk(id);
      logger.info(`Language updated: ${id}`);

      res.json(successResponse(language));
    } catch (error) {
      logger.error(`Error updating language ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/languages/{id}:
   *   delete:
   *     summary: Delete a language
   *     tags: [Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Language deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.Language.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Language not found" }));
        return;
      }

      logger.info(`Language deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete language" }),
        );
    }
  };
}
