import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateBotLanguageRequest, UpdateBotLanguageRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class BotLanguageController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bot-languages:
   *   post:
   *     summary: Assign a language to a bot
   *     description: Creates a new bot-language association, enabling the bot to support the specified language
   *     tags: [Bot Languages]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateBotLanguageSchema'
   *           example:
   *             botId: "123e4567-e89b-12d3-a456-************"
   *             langId: "987fcdeb-51a2-43d1-b456-************"
   *     responses:
   *       201:
   *         description: Language assigned to bot successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/BotLanguageSchema'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public create = async (
    req: Request<any, any, CreateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const botLanguage = await this.models.BotLanguage.create({
        ...req.body,
        createdBy: userId,
      });

      logger.info(`Bot language assigned: ${botLanguage.id}`);
      res.status(201).json(successResponse(botLanguage));
    } catch (error) {
      logger.error("Error assigning bot language:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages:
   *   get:
   *     summary: Get all bot languages
   *     description: Retrieves a paginated list of bot-language associations with optional filtering
   *     tags: [Bot Languages]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: Number of items per page
   *       - in: query
   *         name: botId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by bot ID
   *       - in: query
   *         name: langId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by language ID
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., bot,language)
   *     responses:
   *       200:
   *         description: Bot languages retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedBotLanguagesSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.BotLanguage, req.query);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching bot languages:", error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot languages" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   get:
   *     summary: Get bot language by ID
   *     description: Retrieves a specific bot-language association by its unique identifier
   *     tags: [Bot Languages]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot language association unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., bot,language)
   *     responses:
   *       200:
   *         description: Bot language retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/BotLanguageSchema'
   *       404:
   *         description: Bot language not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const botLanguage = await this.models.BotLanguage.findOne({
        where: { id },
      });

      if (!botLanguage) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      res.json(successResponse(botLanguage));
    } catch (error) {
      logger.error(`Error fetching bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot language" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   put:
   *     summary: Update a bot language association
   *     description: Updates an existing bot-language association
   *     tags: [Bot Languages]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot language association unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateBotLanguageSchema'
   *           example:
   *             botId: "123e4567-e89b-12d3-a456-************"
   *             langId: "987fcdeb-51a2-43d1-b456-************"
   *     responses:
   *       200:
   *         description: Bot language updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/BotLanguageSchema'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponseSchema'
   *       404:
   *         description: Bot language not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public update = async (
    req: Request<UuidParams, any, UpdateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.BotLanguage.update(
        {
          ...req.body,
          createdBy: userId,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      const botLanguage = await this.models.BotLanguage.findByPk(id);
      logger.info(`Bot language updated: ${id}`);

      res.json(successResponse(botLanguage));
    } catch (error) {
      logger.error(`Error updating bot language ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   delete:
   *     summary: Unassign a language from a bot
   *     description: Removes the bot-language association, disabling the language support for the bot
   *     tags: [Bot Languages]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot language association unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       204:
   *         description: Language unassigned from bot successfully (no content)
   *       404:
   *         description: Bot language association not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.BotLanguage.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      logger.info(`Bot language unassigned: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error unassigning bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ code: "INTERNAL_ERROR", message: "Failed to unassign bot language" }),
        );
    }
  };
}
