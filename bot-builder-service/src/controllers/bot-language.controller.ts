import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateBotLanguageRequest, UpdateBotLanguageRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class BotLanguageController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bot-languages:
   *   post:
   *     summary: Assign a language to a bot
   *     tags: [Bot Languages]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * botId:
                  * type: string
                  * format: uuid
                * langId:
                  * type: string
                  * format: uuid
   *     responses:
   *       201:
   *         description: Language assigned successfully
   */
  public create = async (
    req: Request<any, any, CreateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const botLanguage = await this.models.BotLanguage.create({
        ...req.body,
        createdBy: userId,
      });

      logger.info(`Bot language assigned: ${botLanguage.id}`);
      res.status(201).json(successResponse(botLanguage));
    } catch (error) {
      logger.error("Error assigning bot language:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages:
   *   get:
   *     summary: Get all bot languages
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: botId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: langId
   *         schema: { type: string, format: uuid }
   *     responses:
   *       200:
   *         description: List of bot languages
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.BotLanguage, req.query);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching bot languages:", error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot languages" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   get:
   *     summary: Get bot language by ID
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Bot language object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const botLanguage = await this.models.BotLanguage.findOne({
        where: { id },
      });

      if (!botLanguage) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      res.json(successResponse(botLanguage));
    } catch (error) {
      logger.error(`Error fetching bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot language" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   put:
   *     summary: Update a bot language
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * botId:
                  * type: string
                  * format: uuid
                * langId:
                  * type: string
                  * format: uuid
   *     responses:
   *       200:
   *         description: Bot language updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.BotLanguage.update(
        {
          ...req.body,
          createdBy: userId,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      const botLanguage = await this.models.BotLanguage.findByPk(id);
      logger.info(`Bot language updated: ${id}`);

      res.json(successResponse(botLanguage));
    } catch (error) {
      logger.error(`Error updating bot language ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   delete:
   *     summary: Unassign a language from a bot
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Language unassigned successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.BotLanguage.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      logger.info(`Bot language unassigned: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error unassigning bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ code: "INTERNAL_ERROR", message: "Failed to unassign bot language" }),
        );
    }
  };
}
