import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  parseIncludeQuery,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import { CreateIntentItemRequest, UpdateIntentItemRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class IntentItemsController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/intent-items:
   *   post:
   *     summary: Create intent item
   *     tags: [Intent Items]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * name:
                  * type: string
                * description:
                  * type: string
                * botId:
                  * type: string
                  * format: uuid
   *     responses:
   *       201:
   *         description: Intent item created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - type: object
                * properties:
                  * success:
                    * type: boolean
                  * data:
                    * type: object
                  * timestamp:
                    * type: string
                    * format: date-time
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
                        * properties:
                          * id:
                            * type: string
                            * format: uuid
                          * name:
                            * type: string
                          * description:
                            * type: string
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
                * properties:
                  * success:
                    * type: boolean
                    * example: false
                  * error:
                    * type: object
                    * properties:
                      * code:
                        * type: string
                        * example: "VALIDATION_ERROR"
                      * message:
                        * type: string
                      * details:
                        * type: object
                  * timestamp:
                    * type: string
                    * format: date-time
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
                * properties:
                  * success:
                    * type: boolean
                    * example: false
                  * error:
                    * type: object
                    * properties:
                      * code:
                        * type: string
                        * example: "INTERNAL_ERROR"
                      * message:
                        * type: string
                  * timestamp:
                    * type: string
                    * format: date-time
   */
  public create = async (
    req: Request<any, any, CreateIntentItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const intentItem = await this.models.IntentItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Intent item created: ${intentItem.id}`);
      res.status(201).json(successResponse(intentItem));
    } catch (error) {
      logger.error("Error creating intent item:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items:
   *   get:
   *     summary: List intent items with filtering and pagination
   *     tags: [Intent Items]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"botId":{"eq":"uuid"}}'
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Success
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const result = await getPaginatedResults(
        this.models.IntentItems,
        req.query,
        ["name"],
        includeAssociations,
      );

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching intent items:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch intent items" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   get:
   *     summary: Get intent item by ID
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Success
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const intentItem = await this.models.IntentItems.findOne({
        where: { id } as WhereOptions,
        include: includeAssociations,
      });

      if (!intentItem) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      res.json(successResponse(intentItem));
    } catch (error) {
      logger.error(`Error fetching intent item ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch intent item" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   put:
   *     summary: Update intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentUtterances,entities)
   *     responses:
   *       200:
   *         description: Updated
   */
  public update = async (
    req: Request<UuidParams, any, UpdateIntentItemRequest, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.IntentItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const intentItem = await this.models.IntentItems.findByPk(id, {
        include: includeAssociations,
      });
      logger.info(`Intent item updated: ${id}`);

      res.json(successResponse(intentItem));
    } catch (error) {
      logger.error(`Error updating intent item ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-items/{id}:
   *   delete:
   *     summary: Delete intent item
   *     tags: [Intent Items]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.IntentItems.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent item not found" }));
        return;
      }

      logger.info(`Intent item deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting intent item ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete intent item" }),
        );
    }
  };
}
