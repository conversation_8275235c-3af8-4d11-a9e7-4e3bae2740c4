import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  ApiResponse,
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  parseIncludeQuery,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import { CreateIntentUtteranceRequest, UpdateIntentUtteranceRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class IntentUtteranceController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/intent-utterances:
   *   post:
   *     summary: Create a new intent utterance
   *     tags: [Intent Utterances]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * text:
                  * type: string
                * entities:
                  * type: object
   *     responses:
   *       201:
   *         description: Intent utterance created successfully
   */
  public create = async (
    req: Request<any, any, CreateIntentUtteranceRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const intentUtterance = await this.models.IntentUtterance.create({
        ...req.body,
        createdBy: userId,
      });

      logger.info(`Intent utterance created: ${intentUtterance.id}`);
      res.status(201).json(successResponse(intentUtterance));
    } catch (error) {
      logger.error("Error creating intent utterance:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterances:
   *   get:
   *     summary: Get all intent utterances
   *     tags: [Intent Utterances]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: intentId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem,utteranceTranslations)
   *     responses:
   *       200:
   *         description: List of intent utterances
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const result = await getPaginatedResults(
        this.models.IntentUtterance,
        req.query,
        [],
        includeAssociations,
      );

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching intent utterances:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent utterances",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterances/{id}:
   *   get:
   *     summary: Get intent utterance by ID
   *     tags: [Intent Utterances]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem,utteranceTranslations)
   *     responses:
   *       200:
   *         description: Intent utterance object
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const intentUtterance = await this.models.IntentUtterance.findOne({
        where: { id } as WhereOptions,
        include: includeAssociations,
      });

      if (!intentUtterance) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent utterance not found" }));
        return;
      }

      res.json(successResponse(intentUtterance));
    } catch (error) {
      logger.error(`Error fetching intent utterance ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent utterance",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterances/{id}:
   *   put:
   *     summary: Update an intent utterance
   *     tags: [Intent Utterances]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * text:
                  * type: string
                * entities:
                  * type: object
   *     responses:
   *       200:
   *         description: Intent utterance updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateIntentUtteranceRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.IntentUtterance.update(
        {
          ...req.body,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent utterance not found" }));
        return;
      }

      const intentUtterance = await this.models.IntentUtterance.findByPk(id);
      logger.info(`Intent utterance updated: ${id}`);

      res.json(successResponse(intentUtterance));
    } catch (error) {
      logger.error(`Error updating intent utterance ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterances/{id}:
   *   delete:
   *     summary: Delete an intent utterance
   *     tags: [Intent Utterances]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Intent utterance deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.IntentUtterance.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Intent utterance not found" }));
        return;
      }

      logger.info(`Intent utterance deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting intent utterance ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete intent utterance",
        }),
      );
    }
  };
}
