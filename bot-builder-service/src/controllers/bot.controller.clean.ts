/**
 * Bot Controller
 *
 * Handles HTTP requests for bot management operations.
 */

import { Request, Response } from "express";
import { AppContext } from "../types/context.types";
import { BotService } from "../services/bot.service";
import { CreateBotRequest, UpdateBotRequest, BuildBotResponse } from "../types";
import {
  getPaginatedResults,
  logger,
  PaginationQuery,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { Models } from "@neuratalk/bot-store";

export class BotController {
  private botService: BotService;
  private models: Models;

  constructor(context: AppContext) {
    this.botService = context.botService;
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bots:
   *   post:
   *     summary: Create a new bot
   *     description: Creates a new chatbot with the specified configuration
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateBotRequest'
   *           example:
   *             name: "Customer Support Bot"
   *             description: "Handles customer inquiries and support requests"
   *             settings:
   *               nlu:
   *                 provider: "rasa"
   *                 confidenceThreshold: 0.7
   *               fallback:
   *                 enabled: true
   *                 message: "I didn't understand that. Can you please rephrase?"
   *             metadata:
   *               category: "support"
   *               version: "1.0"
   *     responses:
   *       201:
   *         description: Bot created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Bot'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public createBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const bot = await this.botService.createBot(req.body);
      res.status(201).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in createBot controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to create bot",
          }),
        );
      }
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   get:
   *     summary: Get bot by ID
   *     description: Retrieves a specific bot by its unique identifier
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Bot'
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public getBotById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const bot = await this.botService.getBotById(id);

      if (!bot) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "Bot not found",
          }),
        );
        return;
      }

      res.status(200).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in getBotById controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to retrieve bot",
          }),
        );
      }
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   put:
   *     summary: Update a bot
   *     description: Updates an existing bot with the provided data
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateBotRequest'
   *           example:
   *             name: "Updated Customer Support Bot"
   *             description: "Updated description for customer support bot"
   *             settings:
   *               nlu:
   *                 provider: "rasa"
   *                 confidenceThreshold: 0.8
   *     responses:
   *       200:
   *         description: Bot updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/Bot'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponse'
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public updateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const bot = await this.botService.updateBot(id, req.body);

      if (!bot) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "Bot not found",
          }),
        );
        return;
      }

      res.status(200).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in updateBot controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to update bot",
          }),
        );
      }
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}:
   *   delete:
   *     summary: Delete a bot
   *     description: Permanently deletes a bot and all its associated data
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       204:
   *         description: Bot deleted successfully (no content)
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  public deleteBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.botService.deleteBot(id);

      if (!deleted) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "Bot not found",
          }),
        );
        return;
      }

      res.status(204).send();
    } catch (error) {
      logger.error("Error in deleteBot controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to delete bot",
          }),
        );
      }
    }
  };

  /**
   * @swagger
   * /api/v1/bots:
   *   get:
   *     summary: Get all bots
   *     description: Retrieves a paginated list of all bots with optional search functionality
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *         example: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: Number of items per page
   *         example: 10
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term to filter bots by name
   *         example: "support"
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include
   *         example: "languages,flows"
   *     responses:
   *       200:
   *         description: Bots retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedBots'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  getBots = async (req: Request<any, any, any, PaginationQuery>, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, search, include } = req.query;
      const offset = (page - 1) * limit;

      const whereClause: any = {};
      if (search) {
        whereClause.name = { [this.models.Bot.sequelize!.Sequelize.Op.iLike]: `%${search}%` };
      }

      const includeOptions = [];
      if (include) {
        const includeArray = include.split(",");
        if (includeArray.includes("languages")) {
          includeOptions.push({ model: this.models.Language, through: { attributes: [] } });
        }
        if (includeArray.includes("flows")) {
          includeOptions.push({ model: this.models.Flow });
        }
      }

      const { count, rows } = await this.models.Bot.findAndCountAll({
        where: whereClause,
        include: includeOptions,
        limit,
        offset,
        order: [["createdAt", "DESC"]],
      });

      const paginatedResult = getPaginatedResults({
        items: rows,
        total: count,
        page,
        limit,
      });

      res.status(200).json(successResponse(paginatedResult));
    } catch (error) {
      logger.error("Error in getBots controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to retrieve bots",
          }),
        );
      }
    }
  };

  /**
   * @swagger
   * /api/v1/bots/{id}/activate:
   *   post:
   *     summary: Activate a bot
   *     description: Activates a bot, making it available for conversations
   *     tags: [Bots]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot unique identifier
   *         example: "123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Bot activated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           format: uuid
   *                         name:
   *                           type: string
   *                         isActive:
   *                           type: boolean
   *                           example: true
   *                         updatedAt:
   *                           type: string
   *                           format: date-time
   *       404:
   *         description: Bot not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponse'
   */
  activateBot = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const [updatedCount] = await this.models.Bot.update(
        { isActive: true },
        { where: { id }, returning: true },
      );

      if (updatedCount === 0) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "Bot not found",
          }),
        );
        return;
      }

      const bot = await this.models.Bot.findByPk(id);
      res.status(200).json(successResponse(bot));
    } catch (error) {
      logger.error("Error in activateBot controller:", {
        error,
        requestId: (req as any).requestId,
      });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to activate bot",
          }),
        );
      }
    }
  };
}
