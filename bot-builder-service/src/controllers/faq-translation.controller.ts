import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import { CreateFaqTranslationRequest, UpdateFaqTranslationRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class FaqTranslationController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/faq-translations:
   *   post:
   *     summary: Create a new FAQ translation
   *     tags: [FAQ Translations]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * questions:
                  * type: array
                  * items:
                    * type: string
                * answer:
                  * type: string
   *     responses:
   *       201:
   *         description: FAQ translation created successfully
   */
  public create = async (
    req: Request<any, any, CreateFaqTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const faqTranslation = await this.models.FaqTranslation.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ translation created: ${faqTranslation.id}`);
      res.status(201).json(successResponse(faqTranslation));
    } catch (error) {
      logger.error("Error creating FAQ translation:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations:
   *   get:
   *     summary: Get all FAQ translations
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: faqId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: langId
   *         schema: { type: string, format: uuid }
   *     responses:
   *       200:
   *         description: List of FAQ translations
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.FaqTranslation, req.query);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching FAQ translations:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ translations",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   get:
   *     summary: Get FAQ translation by ID
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: FAQ translation object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const faqTranslation = await this.models.FaqTranslation.findOne({
        where: { id },
      });

      if (!faqTranslation) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      res.json(successResponse(faqTranslation));
    } catch (error) {
      logger.error(`Error fetching FAQ translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch FAQ translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   put:
   *     summary: Update an FAQ translation
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
              * properties:
                * questions:
                  * type: array
                  * items:
                    * type: string
                * answer:
                  * type: string
   *     responses:
   *       200:
   *         description: FAQ translation updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateFaqTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.FaqTranslation.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      const faqTranslation = await this.models.FaqTranslation.findByPk(id);
      logger.info(`FAQ translation updated: ${id}`);

      res.json(successResponse(faqTranslation));
    } catch (error) {
      logger.error(`Error updating FAQ translation ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-translations/{id}:
   *   delete:
   *     summary: Delete an FAQ translation
   *     tags: [FAQ Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: FAQ translation deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqTranslation.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "FAQ translation not found" }));
        return;
      }

      logger.info(`FAQ translation deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete FAQ translation",
        }),
      );
    }
  };
}
