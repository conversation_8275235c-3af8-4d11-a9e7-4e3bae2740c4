import { z } from "zod";
import { extendZod<PERSON>ith<PERSON>pen<PERSON>pi } from "@asteasolutions/zod-to-openapi";
import { UuidSchema, PaginationQuerySchema } from "@neuratalk/common";

// Extend Zod with OpenAPI support
extendZodWithOpenApi(z);

// Bot Schemas
export const CreateBotRequestSchema = z
  .object({
    name: z.string().min(1).max(255).openapi({
      example: "Customer Support Bot",
      description: "The name of the bot",
    }),
    description: z.string().max(1000).optional().openapi({
      example: "Handles customer inquiries and support requests",
      description: "Optional description of the bot's purpose",
    }),
    settings: z
      .record(z.any())
      .optional()
      .openapi({
        example: {
          nlu: {
            provider: "rasa",
            confidenceThreshold: 0.7,
          },
          responses: {
            defaultLanguage: "en",
            fallbackMessage: "I didn't understand that. Can you please rephrase?",
          },
        },
        description: "Bot configuration settings",
      }),
    metadata: z
      .record(z.any())
      .optional()
      .openapi({
        example: {
          category: "customer_service",
          version: "1.0.0",
          tags: ["support", "automated"],
        },
        description: "Additional metadata for the bot",
      }),
  })
  .strict()
  .openapi({
    example: {
      name: "Customer Support Bot",
      description: "Handles customer inquiries and support requests",
      settings: {
        nlu: {
          provider: "rasa",
          confidenceThreshold: 0.7,
        },
      },
      metadata: {
        category: "customer_service",
        version: "1.0.0",
      },
    },
  });

export const UpdateBotRequestSchema = CreateBotRequestSchema.partial().strict();

export const BotIdParamSchema = z
  .object({
    id: UuidSchema,
  })
  .strict();

// Channel Integration Schemas
export const BotChannelParamSchema = z
  .object({
    botId: UuidSchema,
    channelType: z.string().min(1).openapi({
      example: "whatsapp",
      description: "Channel type (web, whatsapp, telegram, slack)",
    }),
  })
  .strict();

export const CreateChannelIntegrationSchema = z
  .object({
    channelType: z.string().min(1).openapi({
      example: "whatsapp",
      description: "Type of channel integration",
    }),
    config: z.record(z.any()).openapi({
      example: {
        apiKey: "your-api-key",
        webhookUrl: "https://your-domain.com/webhook",
        phoneNumber: "+**********",
      },
      description: "Channel-specific configuration",
    }),
  })
  .strict()
  .openapi({
    example: {
      channelType: "whatsapp",
      config: {
        apiKey: "your-whatsapp-api-key",
        webhookUrl: "https://your-domain.com/webhook/whatsapp",
        phoneNumber: "+**********",
      },
    },
  });

export const BotChannelIdParamSchema = z
  .object({
    botId: UuidSchema,
    channelId: UuidSchema,
  })
  .strict();

export const UpdateChannelIntegrationSchema = CreateChannelIntegrationSchema.partial().strict();

// Flow Schemas
export const CreateFlowRequestSchema = z
  .object({
    name: z.string().min(1).max(255),
    description: z.string().max(1000).optional(),
    botId: UuidSchema,
    type: z.string().optional(),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const FlowIdParamSchema = z
  .object({
    id: UuidSchema,
  })
  .strict();

export const UpdateFlowRequestSchema = CreateFlowRequestSchema.partial().strict();

export const FlowAppIdParamSchema = z
  .object({
    id: UuidSchema,
    appId: UuidSchema,
  })
  .strict();

export const GetFlowsQuerySchema = PaginationQuerySchema.extend({
  botId: UuidSchema.optional(),
  isActive: z.boolean().optional(),
}).strict();

export const BulkCreateFlowsRequestSchema = z
  .object({
    flows: z.array(CreateFlowRequestSchema),
  })
  .strict();

// Type exports
export type CreateBotRequest = z.infer<typeof CreateBotRequestSchema>;
export type UpdateBotRequest = z.infer<typeof UpdateBotRequestSchema>;
export type BotIdParam = z.infer<typeof BotIdParamSchema>;
export type BotChannelParam = z.infer<typeof BotChannelParamSchema>;
export type CreateChannelIntegrationRequest = z.infer<typeof CreateChannelIntegrationSchema>;
export type BotChannelIdParam = z.infer<typeof BotChannelIdParamSchema>;
export type UpdateChannelIntegrationRequest = z.infer<typeof UpdateChannelIntegrationSchema>;
export type CreateFlowRequest = z.infer<typeof CreateFlowRequestSchema>;
export type FlowIdParam = z.infer<typeof FlowIdParamSchema>;
export type UpdateFlowRequest = z.infer<typeof UpdateFlowRequestSchema>;
export type FlowAppIdParam = z.infer<typeof FlowAppIdParamSchema>;
export type GetFlowsQuery = z.infer<typeof GetFlowsQuerySchema>;
export type BulkCreateFlowsRequest = z.infer<typeof BulkCreateFlowsRequestSchema>;
