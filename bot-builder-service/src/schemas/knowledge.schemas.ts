import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// FAQ Category schemas
export const CreateFaqCategorySchema = z
  .object({
    botId: UuidSchema,
    name: z.string().min(1).max(255),
    description: z.string().optional(),
  })
  .strict();

export const UpdateFaqCategorySchema = CreateFaqCategorySchema.partial().strict();

// FAQ Item schemas
export const CreateFaqItemSchema = z
  .object({
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    categoryId: UuidSchema,
  })
  .strict();

export const UpdateFaqItemSchema = CreateFaqItemSchema.partial().strict();

// FAQ Translation schemas
export const CreateFaqTranslationSchema = z
  .object({
    faqId: UuidSchema,
    langId: UuidSchema,
    questions: z.array(z.string().min(1)).min(1),
    answer: z.string().min(1),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateFaqTranslationSchema = CreateFaqTranslationSchema.partial().strict();

// Intent Item schemas
export const CreateIntentItemSchema = z
  .object({
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    name: z.string().min(1).max(255),
    description: z.string().optional(),
  })
  .strict();

export const UpdateIntentItemSchema = CreateIntentItemSchema.partial().strict();

// Intent Utterance schemas
export const CreateIntentUtteranceSchema = z
  .object({
    intentId: UuidSchema,
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateIntentUtteranceSchema = CreateIntentUtteranceSchema.partial().strict();

// Intent Utterance Translation schemas
export const CreateIntentUtteranceTranslationSchema = z
  .object({
    utteranceId: UuidSchema,
    langId: UuidSchema,
    text: z.string().min(1),
    entities: z.record(z.any()).optional(), // Or a more specific type for entities
  })
  .strict();

export const UpdateIntentUtteranceTranslationSchema =
  CreateIntentUtteranceTranslationSchema.partial().strict();

// Entity schemas
export const CreateEntitySchema = z
  .object({
    botId: UuidSchema,
    intentId: UuidSchema,
    name: z.string().min(1).max(100),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateEntitySchema = CreateEntitySchema.partial().strict();

// Response schemas for entities
export const EntityResponseSchema = z
  .object({
    id: UuidSchema,
    name: z.string(),
    botId: UuidSchema,
    intentId: UuidSchema,
    metadata: z.record(z.any()).optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
    // Optional associations
    bot: z.any().optional(),
    intentItem: z.any().optional(),
  })
  .strict();

export const EntityListResponseSchema = z
  .object({
    items: z.array(EntityResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for FAQ Categories
export const FaqCategoryResponseSchema = z
  .object({
    id: UuidSchema,
    botId: UuidSchema,
    name: z.string(),
    description: z.string().optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const FaqCategoryListResponseSchema = z
  .object({
    items: z.array(FaqCategoryResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for FAQ Items
export const FaqItemResponseSchema = z
  .object({
    id: UuidSchema,
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    categoryId: UuidSchema,
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const FaqItemListResponseSchema = z
  .object({
    items: z.array(FaqItemResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for FAQ Translations
export const FaqTranslationResponseSchema = z
  .object({
    id: UuidSchema,
    faqId: UuidSchema,
    langId: UuidSchema,
    questions: z.array(z.string()),
    answer: z.string(),
    metadata: z.record(z.any()).optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const FaqTranslationListResponseSchema = z
  .object({
    items: z.array(FaqTranslationResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for Intent Items
export const IntentItemResponseSchema = z
  .object({
    id: UuidSchema,
    botId: UuidSchema,
    flowId: UuidSchema.optional(),
    name: z.string(),
    description: z.string().optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const IntentItemListResponseSchema = z
  .object({
    items: z.array(IntentItemResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for Intent Utterances
export const IntentUtteranceResponseSchema = z
  .object({
    id: UuidSchema,
    intentId: UuidSchema,
    metadata: z.record(z.any()).optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const IntentUtteranceListResponseSchema = z
  .object({
    items: z.array(IntentUtteranceResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for Intent Utterance Translations
export const IntentUtteranceTranslationResponseSchema = z
  .object({
    id: UuidSchema,
    utteranceId: UuidSchema,
    langId: UuidSchema,
    text: z.string(),
    entities: z.record(z.any()).optional(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
  })
  .strict();

export const IntentUtteranceTranslationListResponseSchema = z
  .object({
    items: z.array(IntentUtteranceTranslationResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Type extraction
export type CreateFaqCategoryRequest = z.infer<typeof CreateFaqCategorySchema>;
export type UpdateFaqCategoryRequest = z.infer<typeof UpdateFaqCategorySchema>;
export type CreateFaqItemRequest = z.infer<typeof CreateFaqItemSchema>;
export type UpdateFaqItemRequest = z.infer<typeof UpdateFaqItemSchema>;
export type CreateFaqTranslationRequest = z.infer<typeof CreateFaqTranslationSchema>;
export type UpdateFaqTranslationRequest = z.infer<typeof UpdateFaqTranslationSchema>;
export type CreateIntentItemRequest = z.infer<typeof CreateIntentItemSchema>;
export type UpdateIntentItemRequest = z.infer<typeof UpdateIntentItemSchema>;
export type CreateIntentUtteranceRequest = z.infer<typeof CreateIntentUtteranceSchema>;
export type UpdateIntentUtteranceRequest = z.infer<typeof UpdateIntentUtteranceSchema>;
export type CreateIntentUtteranceTranslationRequest = z.infer<
  typeof CreateIntentUtteranceTranslationSchema
>;
export type UpdateIntentUtteranceTranslationRequest = z.infer<
  typeof UpdateIntentUtteranceTranslationSchema
>;
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;

// Response type extraction
export type EntityResponse = z.infer<typeof EntityResponseSchema>;
export type EntityListResponse = z.infer<typeof EntityListResponseSchema>;
export type FaqCategoryResponse = z.infer<typeof FaqCategoryResponseSchema>;
export type FaqCategoryListResponse = z.infer<typeof FaqCategoryListResponseSchema>;
export type FaqItemResponse = z.infer<typeof FaqItemResponseSchema>;
export type FaqItemListResponse = z.infer<typeof FaqItemListResponseSchema>;
export type FaqTranslationResponse = z.infer<typeof FaqTranslationResponseSchema>;
export type FaqTranslationListResponse = z.infer<typeof FaqTranslationListResponseSchema>;
export type IntentItemResponse = z.infer<typeof IntentItemResponseSchema>;
export type IntentItemListResponse = z.infer<typeof IntentItemListResponseSchema>;
export type IntentUtteranceResponse = z.infer<typeof IntentUtteranceResponseSchema>;
export type IntentUtteranceListResponse = z.infer<typeof IntentUtteranceListResponseSchema>;
export type IntentUtteranceTranslationResponse = z.infer<
  typeof IntentUtteranceTranslationResponseSchema
>;
export type IntentUtteranceTranslationListResponse = z.infer<
  typeof IntentUtteranceTranslationListResponseSchema
>;
