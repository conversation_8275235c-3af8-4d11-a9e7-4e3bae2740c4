# Swagger Auto-Generation Guide

This document provides comprehensive prompts and guidelines for AI models to automatically generate, update, and maintain Swagger/OpenAPI documentation for the bot-builder-service whenever code structure changes occur.

## 🎯 Core Objective

Automatically maintain accurate, comprehensive, and elegant Swagger documentation that stays synchronized with code changes without manual developer intervention.

## 📋 Auto-Generation Prompt Template

### Primary Prompt for AI Models

```
You are a Swagger/OpenAPI documentation specialist. Your task is to automatically generate and maintain comprehensive API documentation for a Node.js/TypeScript service whenever code changes are detected.

CONTEXT:
- Service: bot-builder-service (chatbot management API)
- Framework: Express.js with TypeScript
- Validation: Zod schemas with @asteasolutions/zod-to-openapi
- Documentation: OpenAPI 3.0 specification

WHEN TO TRIGGER:
- New controller methods added
- Existing endpoints modified
- Route changes in router files
- Schema updates in Zod definitions
- New API endpoints created
- Response structure changes

REQUIREMENTS:
1. Generate complete @swagger JSDoc comments for all endpoints
2. Use proper OpenAPI 3.0 YAML syntax (no malformed indentation)
3. Include comprehensive examples from Zod schemas
4. Maintain consistent response structures
5. Document all parameters, request bodies, and responses
6. Include proper error responses (400, 404, 500)
7. Use appropriate HTTP status codes
8. Tag endpoints logically
9. Include security schemes where needed
10. Ensure examples are realistic and helpful

FOLLOW THESE PATTERNS:
```

### Detailed Generation Rules

## 🔧 Code Analysis Patterns

### 1. Controller Method Detection

```typescript
// DETECT: New controller methods
export class BotController {
  // PATTERN: public methodName = async (req: Request, res: Response)
  public createBot = async (req: Request, res: Response) => {
    // AUTO-GENERATE: Complete Swagger documentation
  };
}
```

### 2. Route Definition Detection

```typescript
// DETECT: Router configurations
router.post("/bots", validateBody(CreateBotRequestSchema), botController.createBot);
// AUTO-GENERATE: Corresponding Swagger path documentation
```

### 3. Schema Change Detection

```typescript
// DETECT: Zod schema modifications
export const CreateBotRequestSchema = z.object({
  name: z.string().openapi({ example: "Bot Name" }),
});
// AUTO-UPDATE: Swagger components and examples
```

## 📝 Swagger Documentation Templates

### Template 1: POST Endpoint

```yaml
/**
 * @swagger
 * /api/v1/{resource}:
 *   post:
 *     summary: Create a new {resource}
 *     description: Creates a new {resource} with the provided data
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{CreateResourceSchema}'
 *     responses:
 *       201:
 *         description: {Resource} created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{ResourceModel}'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

### Template 2: GET Endpoint (Single Resource)

```yaml
/**
 * @swagger
 * /api/v1/{resource}/{id}:
 *   get:
 *     summary: Get {resource} by ID
 *     description: Retrieves a specific {resource} by its unique identifier
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: {Resource} unique identifier
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: {Resource} retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{ResourceModel}'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

### Template 3: GET Endpoint (List with Pagination)

```yaml
/**
 * @swagger
 * /api/v1/{resource}:
 *   get:
 *     summary: Get all {resource}s
 *     description: Retrieves a paginated list of {resource}s
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for filtering
 *     responses:
 *       200:
 *         description: {Resource}s retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/{ResourceModel}'
 *                     pagination:
 *                       $ref: '#/components/schemas/PaginationInfo'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

### Template 4: PUT Endpoint

```yaml
/**
 * @swagger
 * /api/v1/{resource}/{id}:
 *   put:
 *     summary: Update {resource}
 *     description: Updates an existing {resource} with new data
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: {Resource} unique identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{UpdateResourceSchema}'
 *     responses:
 *       200:
 *         description: {Resource} updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{ResourceModel}'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

### Template 5: DELETE Endpoint

```yaml
/**
 * @swagger
 * /api/v1/{resource}/{id}:
 *   delete:
 *     summary: Delete {resource}
 *     description: Deletes an existing {resource}
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: {Resource} unique identifier
 *     responses:
 *       204:
 *         description: {Resource} deleted successfully
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

## 🏗️ Component Templates

### Standard Response Components

```yaml
components:
  responses:
    BadRequest:
      description: Invalid request data
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              error:
                type: object
                properties:
                  code:
                    type: string
                    example: "VALIDATION_ERROR"
                  message:
                    type: string
                    example: "Invalid input data"
                  details:
                    type: array
                    items:
                      type: object
              timestamp:
                type: string
                format: date-time

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              error:
                type: object
                properties:
                  code:
                    type: string
                    example: "NOT_FOUND"
                  message:
                    type: string
                    example: "Resource not found"
              timestamp:
                type: string
                format: date-time

    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              error:
                type: object
                properties:
                  code:
                    type: string
                    example: "UNAUTHORIZED"
                  message:
                    type: string
                    example: "Authentication required"
              timestamp:
                type: string
                format: date-time

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: false
              error:
                type: object
                properties:
                  code:
                    type: string
                    example: "INTERNAL_ERROR"
                  message:
                    type: string
                    example: "An internal error occurred"
              timestamp:
                type: string
                format: date-time

  schemas:
    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        total:
          type: integer
          example: 100
        totalPages:
          type: integer
          example: 10
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false
```

## 🤖 AI Model Instructions

### Step-by-Step Process

1. **Code Analysis Phase**

   ```
   ANALYZE:
   - Scan all controller files for new/modified methods
   - Check router files for route changes
   - Examine Zod schemas for structure updates
   - Identify missing Swagger documentation
   ```

2. **Documentation Generation Phase**

   ```
   GENERATE:
   - Create complete @swagger JSDoc comments
   - Use appropriate templates based on HTTP method
   - Extract examples from Zod schemas
   - Ensure proper YAML formatting
   - Include all required response codes
   ```

3. **Validation Phase**

   ```
   VALIDATE:
   - Check YAML syntax correctness
   - Verify all references exist
   - Ensure examples are realistic
   - Confirm response structure consistency
   ```

4. **Integration Phase**
   ```
   INTEGRATE:
   - Update swagger configuration
   - Register new schemas
   - Test documentation generation
   - Verify Swagger UI functionality
   ```

### Quality Checklist

- [ ] All endpoints have complete documentation
- [ ] YAML syntax is valid (no malformed indentation)
- [ ] Examples are extracted from Zod schemas
- [ ] Response structures are consistent
- [ ] Error responses are documented
- [ ] Security schemes are applied
- [ ] Tags are logical and consistent
- [ ] Parameters are fully described
- [ ] Request bodies reference correct schemas
- [ ] Components are reusable

## 🔄 Automation Triggers

### File Change Triggers

```bash
# Monitor these file patterns for changes:
src/controllers/**/*.ts
src/routers/**/*.ts
src/schemas/**/*.ts
src/types/**/*.ts
```

### Change Detection Rules

```typescript
// Trigger documentation update when:
1. New controller method added
2. Route definition changed
3. Zod schema modified
4. New endpoint created
5. Response structure updated
6. Parameter changes detected
```

## 📊 Example Implementation

### Before (Malformed)

```typescript
/**
 * @swagger
 * /api/v1/bots:
 *   post:
 *     schema:
 *       type: object
 * properties:
 * name:
 * type: string  // WRONG: Invalid YAML
 */
```

### After (Correct)

```typescript
/**
 * @swagger
 * /api/v1/bots:
 *   post:
 *     summary: Create a new bot
 *     tags: [Bots]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBotRequest'
 *     responses:
 *       201:
 *         description: Bot created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BotResponse'
 */
```

## 🎯 Success Metrics

- ✅ Zero malformed YAML in Swagger documentation
- ✅ 100% endpoint coverage
- ✅ Consistent response structures
- ✅ Realistic examples from Zod schemas
- ✅ Proper error response documentation
- ✅ Functional Swagger UI
- ✅ No manual documentation maintenance required

## 🚀 Usage Instructions

1. **For AI Models**: Use this guide as context when detecting code changes
2. **For Developers**: Reference templates when manually adding documentation
3. **For CI/CD**: Integrate validation checks for documentation completeness
4. **For Code Reviews**: Ensure new endpoints include proper documentation

---

_This guide ensures that Swagger documentation remains accurate, comprehensive, and automatically maintained without developer intervention._
