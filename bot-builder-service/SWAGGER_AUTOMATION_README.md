# 🤖 Swagger Auto-Generation System

This system automatically generates and maintains comprehensive Swagger/OpenAPI documentation for the bot-builder-service without requiring manual developer intervention.

## 📁 Files Overview

| File                                | Purpose                                            |
| ----------------------------------- | -------------------------------------------------- |
| `SWAGGER_AUTO_GENERATION_GUIDE.md`  | Comprehensive guide and templates for AI models    |
| `AI_SWAGGER_PROMPT.md`              | Quick AI prompt for immediate fixes                |
| `scripts/auto-swagger-generator.js` | Automation script for file watching and generation |
| `SWAGGER_AUTOMATION_README.md`      | This file - usage instructions                     |

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install chokidar --save-dev
```

### 2. Fix Current Issues

```bash
# Validate current documentation
npm run swagger:validate

# Start watching for changes
npm run swagger:watch
```

### 3. For AI Models

Use the `AI_SWAGGER_PROMPT.md` file as context when processing code changes.

## 🛠️ Available Commands

```bash
# Watch for file changes and auto-generate docs
npm run swagger:watch

# Validate current Swagger documentation
npm run swagger:validate

# Generate documentation for all files
npm run swagger:generate

# Get instructions for AI-powered fixes
npm run swagger:fix

# Build and validate documentation
npm run docs:generate
```

## 🎯 Current Issues & Solutions

### Issue 1: Malformed YAML Syntax

**Problem:** Swagger comments have invalid YAML with `*` characters

```yaml
# BROKEN:
* properties:
  * name:
    * type: string
```

**Solution:** Use proper YAML indentation

```yaml
# FIXED:
properties:
  name:
    type: string
```

### Issue 2: Missing Endpoints

**Problem:** Only 18 endpoints documented, many bot routes missing

**Solution:** Auto-generate documentation for all controller methods

### Issue 3: Inconsistent Examples

**Problem:** Hardcoded examples not matching Zod schemas

**Solution:** Extract examples from Zod schemas using `.openapi()` method

## 🔧 How It Works

### 1. File Watching

The system monitors these patterns:

- `src/controllers/**/*.ts`
- `src/routers/**/*.ts`
- `src/schemas/**/*.ts`

### 2. Change Detection

Triggers on:

- New controller methods
- Route modifications
- Schema updates
- Missing documentation

### 3. Auto-Generation

- Analyzes method signatures
- Infers HTTP methods and paths
- Generates complete Swagger docs
- Uses proper OpenAPI 3.0 syntax
- Extracts examples from Zod schemas

### 4. Validation

- Checks YAML syntax
- Verifies schema references
- Tests Swagger UI functionality

## 📋 Templates Used

### POST Endpoint Template

```yaml
/**
 * @swagger
 * /api/v1/{resource}:
 *   post:
 *     summary: Create a new {resource}
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{CreateResourceSchema}'
 *     responses:
 *       201:
 *         description: {Resource} created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/{ResourceModel}'
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
```

## 🎨 Zod Schema Enhancement

### Before

```typescript
export const CreateBotRequestSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
});
```

### After (With Examples)

```typescript
export const CreateBotRequestSchema = z
  .object({
    name: z.string().min(1).max(255).openapi({
      example: "Customer Support Bot",
      description: "The name of the bot",
    }),
    description: z.string().optional().openapi({
      example: "Handles customer inquiries and support requests",
      description: "Optional description of the bot's purpose",
    }),
  })
  .openapi({
    example: {
      name: "Customer Support Bot",
      description: "Handles customer inquiries and support requests",
    },
  });
```

## 🤖 AI Integration

### For ChatGPT/Claude/Other AI Models

1. **Load Context:** Use `AI_SWAGGER_PROMPT.md` as system prompt
2. **Analyze Code:** Scan controller files for issues
3. **Apply Fixes:** Use templates from `SWAGGER_AUTO_GENERATION_GUIDE.md`
4. **Validate:** Ensure YAML syntax is correct

### Example AI Workflow

```
1. User: "Fix Swagger documentation for bot-builder-service"
2. AI: Loads AI_SWAGGER_PROMPT.md as context
3. AI: Scans controllers for malformed YAML
4. AI: Applies proper OpenAPI 3.0 templates
5. AI: Validates and tests documentation
6. AI: Reports completion with endpoint count
```

## 📊 Success Metrics

- ✅ **Zero YAML parsing errors**
- ✅ **100% endpoint coverage**
- ✅ **Consistent response structures**
- ✅ **Examples from Zod schemas**
- ✅ **Functional Swagger UI**
- ✅ **No manual maintenance required**

## 🔍 Troubleshooting

### Swagger UI Not Loading

```bash
# Check for YAML syntax errors
npm run swagger:validate

# Rebuild and test
npm run build
npm run dev
# Visit: http://localhost:3000/api-docs
```

### Missing Endpoints

```bash
# Check if routes are properly registered
npm run swagger:generate

# Verify controller methods have documentation
grep -r "@swagger" src/controllers/
```

### Schema References Broken

```bash
# Ensure schemas are exported in index.ts
cat src/schemas/index.ts

# Check swagger config includes all schemas
cat src/config/swagger.ts
```

## 🚀 Development Workflow

### For New Features

1. Create controller method
2. Add route in router
3. Define Zod schema with examples
4. Run `npm run swagger:watch` (auto-generates docs)
5. Verify in Swagger UI

### For Bug Fixes

1. Modify existing endpoint
2. Update Zod schema if needed
3. Documentation updates automatically
4. Validate with `npm run swagger:validate`

### For AI-Powered Fixes

1. Run `npm run swagger:fix` for instructions
2. Use `AI_SWAGGER_PROMPT.md` with your AI model
3. Let AI scan and fix all issues
4. Validate results with `npm run docs:generate`

## 📈 Benefits

- **Zero Manual Work:** Documentation updates automatically
- **Always Accurate:** Synced with code changes
- **Consistent Quality:** Uses standardized templates
- **Rich Examples:** Extracted from Zod schemas
- **Error Prevention:** Validates YAML syntax
- **Developer Friendly:** No learning curve required

## 🎯 Next Steps

1. **Immediate:** Fix current malformed YAML issues
2. **Short-term:** Implement file watching system
3. **Long-term:** Integrate with CI/CD pipeline
4. **Future:** Add API versioning support

---

_This system ensures your Swagger documentation is always comprehensive, accurate, and automatically maintained! 🚀_
