{"name": "@chatbot/bot-builder-service", "version": "1.0.0", "description": "Administrative service for managing bots and flows", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev -r tsconfig-paths/register --respawn --transpile-only src/index.ts", "build": "tsc", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "migrate": "NODE_OPTIONS='-r ts-node/register' sequelize-cli db:migrate", "migrate:undo": "NODE_OPTIONS='-r ts-node/register' sequelize-cli db:migrate:undo", "migrate:create": "NODE_OPTIONS='-r ts-node/register' sequelize-cli migration:generate --name", "swagger:watch": "node scripts/auto-swagger-generator.js watch", "swagger:validate": "node scripts/auto-swagger-generator.js validate", "swagger:generate": "node scripts/auto-swagger-generator.js generate", "swagger:fix": "node -e \"console.log('🔧 Run AI model with AI_SWAGGER_PROMPT.md to fix documentation')\"", "docs:generate": "npm run build && npm run swagger:validate"}, "keywords": ["chatbot", "admin", "builder", "typescript"], "author": "Chatbot Platform Team", "license": "MIT", "dependencies": {"@neuratalk/bot-store": "file:../packages/bot-store", "@neuratalk/common": "file:../packages/common", "api_gw": "file:../studio/api_gw", "app_validation": "file:../studio/app_validation", "bcrypt": "^5.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.1.4", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.0", "winston": "^3.9.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^20.3.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^8.43.0", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.0", "@asteasolutions/zod-to-openapi": "^7.3.4"}, "engines": {"node": ">=18.0.0"}}