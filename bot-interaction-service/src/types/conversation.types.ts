/**
 * Conversation and Session Management Types
 *
 * These types manage user sessions, conversation context, and state persistence
 * in Redis for the stateless bot-interaction-service.
 */

import { OutgoingMessage } from "./message.types";
import { FlowNode, FormNodeType, MessageNodeType } from "./enum";

// --- Session Context ---

interface ConversationContextMetadata {
  sessionTimeout: number;
}

export interface MessageOptions {
  channelType: ChannelType;
}

export interface ConversationContext {
  chatConversationId: string; // The session ID - primary identifier
  userId?: string; // Optional user identifier
  botId: string; // Which bot this conversation belongs to
  currentFlowId?: string; // Currently executing flow
  currentJourneyId?: string | null;

  waitingForInput?: boolean;
  asyncOperationInProgress?: boolean;

  // Session metadata
  sessionStartedAt: Date;
  lastActivityAt: Date;
  metadata: ConversationContextMetadata;
  expiresAt: Date; // When this session should expire
  preservedContext?: PreservedContext | null;
  journeyContext: JourneyContext;

  // Dynamic context data - collected from forms, API responses, etc.
  [key: string]: any;
}

// --- Conversation Management ---

export interface Conversation {
  id: string; // Same as chatConversationId
  userId?: string;
  botId: string;
  status: "active" | "inactive" | "completed" | "abandoned";
  startedAt: Date;
  endedAt?: Date;
  lastMessageAt: Date;
  messageCount: number;
  metadata?: Record<string, any>;
}

// --- Session Configuration ---

export interface SessionConfig {
  defaultTtlMinutes: number; // Default session TTL in minutes
  maxTtlMinutes: number; // Maximum allowed TTL
  inactivityTimeoutMinutes: number; // Auto-expire after inactivity
  maxConcurrentSessions: number; // Per user limit
  enableSessionPersistence: boolean; // Whether to persist to database
}

// --- Context Operations ---

export interface ContextUpdate {
  chatConversationId: string;
  updates: Record<string, any>;
  operation: "set" | "merge" | "delete";
  ttlMinutes?: number; // Override default TTL
}

export interface ContextQuery {
  chatConversationId: string;
  fields?: string[]; // Specific fields to retrieve
  includeMetadata?: boolean;
}

// --- Session Events ---

export interface SessionEvent {
  type:
    | "session_started"
    | "session_resumed"
    | "session_expired"
    | "session_ended"
    | "context_updated";
  conversationId: string;
  userId?: string;
  botId: string;
  timestamp: Date;
  data?: Record<string, any>;
}

// --- Flow State Management ---

export interface FlowState {
  flowId: string;
  journeyId: string | null;
  // executionStack: string[]; // Stack of flow IDs for nested flows
  // variables: Record<string, any>; // Flow-scoped variables
  // loopCounters: Record<string, number>; // For preventing infinite loops
}

export interface ExecutionContext {
  conversation: ConversationContext;
  flowState: FlowState;
  userMessage?: string;
  systemMessage?: string;
  timestamp: Date;
}

export interface PreservedContext {
  currentFlowId: string;
  currentJourneyId: string;
  preservedContext?: PreservedContext | null;
  journeyContext?: JourneyContext;
}

export enum ChannelType {
  WEB = "web",
  WHATSAPP = "whatsapp",
}

export interface JourneyContext {
  channelType?: ChannelType;
  sessionTimeout?: number;
  formData?: Record<string, Record<string, string>>; //TODO: use better type
  completedForms?: string[];
  awaitingInput?: boolean;
  currentForm?: FormNodeData;
  flowAction?: {
    journeyId: string;
    flowId: string;
    resumeFromThisContext: boolean;
    passExistingContext: boolean;
  };
}

export interface JourneyResult {
  context: JourneyContext;
  messages: ConvNode[];
  success: boolean;
  nodeId: string;
  sessionId: string;
  error?: object; //TODO: make type more conditional working
}

interface FormPromptNode {
  fieldName: string;
  fieldType: string;
  label: string;
  required: boolean;
  options: string[]; //TODO: could change this to handle some complex data type
  formId: string;
}

interface FormNodeData {
  prompt: FormPromptNode[];
  formId: string;
  formType: FormNodeType;
}

type FormNode = {
  nodeType: FlowNode.FORM;
  data: FormNodeData;
};

interface MessageNodeData {
  text: string;
  type: MessageNodeType;
  timestamp?: string;
}
interface MessageNode {
  nodeType: FlowNode.MESSAGE;
  data: MessageNodeData;
}

interface FlowConnectorData {
  journeyId: string;
  flowId: string;
  resumeFromThisContext: boolean;
  passExistingContext: boolean;
}
interface FlowConnector {
  nodeType: FlowNode.FLOW_CONNECTOR;
  flowAction: FlowConnectorData;
}

export type ConvNode = FormNode | MessageNode | FlowConnector;