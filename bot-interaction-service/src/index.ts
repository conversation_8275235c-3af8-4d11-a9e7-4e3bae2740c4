/**
 * Bot Interaction Service Entry Point
 *
 * Main entry point for the bot-interaction-service.
 * Handles graceful startup and shutdown.
 */

import { logger } from "@neuratalk/common";
import { App } from "./app";
import { GracefulShutdown, createShutdownHandler } from "./utils/graceful-shutdown";

// Create application instance
const app = new App();

// Initialize graceful shutdown
const gracefulShutdown = GracefulShutdown.getInstance();

// Register shutdown handlers
gracefulShutdown.register(
  createShutdownHandler(
    "Application",
    async () => {
      await app.stop();
    },
    15000,
  ), // 15 second timeout
);

// Initialize shutdown listeners
gracefulShutdown.init();

// Start the application
app.start().catch((error) => {
  logger.error("Failed to start application:", error);
  process.exit(1);
});

export { app, gracefulShutdown };
