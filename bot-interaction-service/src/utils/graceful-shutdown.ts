/**
 * Graceful Shutdown Handler
 * Ensures clean shutdown of all services and connections
 */

import { logger } from "@neuratalk/common";

export interface ShutdownHandler {
  name: string;
  shutdown: () => Promise<void>;
  timeout?: number;
}

export class GracefulShutdown {
  private static instance: GracefulShutdown;
  private handlers: ShutdownHandler[] = [];
  private isShuttingDown = false;
  private readonly defaultTimeout = 10000; // 10 seconds

  static getInstance(): GracefulShutdown {
    if (!GracefulShutdown.instance) {
      GracefulShutdown.instance = new GracefulShutdown();
    }
    return GracefulShutdown.instance;
  }

  /**
   * Register a shutdown handler
   */
  register(handler: ShutdownHandler): void {
    this.handlers.push(handler);
    logger.debug(`Registered shutdown handler: ${handler.name}`);
  }

  /**
   * Initialize graceful shutdown listeners
   */
  init(): void {
    // Handle SIGTERM (Docker, Kubernetes)
    process.on("SIGTERM", () => {
      logger.info("Received SIGTERM signal");
      this.shutdown("SIGTERM");
    });

    // Handle SIGINT (Ctrl+C)
    process.on("SIGINT", () => {
      logger.info("Received SIGINT signal");
      this.shutdown("SIGINT");
    });

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.error("Uncaught Exception:", error);
      this.shutdown("UNCAUGHT_EXCEPTION");
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason, promise) => {
      logger.error("Unhandled Rejection at:", promise, "reason:", reason);
      this.shutdown("UNHANDLED_REJECTION");
    });

    logger.info("Graceful shutdown handlers initialized");
  }

  /**
   * Execute shutdown sequence
   */
  private async shutdown(signal: string): Promise<void> {
    if (this.isShuttingDown) {
      logger.warn("Shutdown already in progress, ignoring signal:", signal);
      return;
    }

    this.isShuttingDown = true;
    logger.info(`Starting graceful shutdown due to: ${signal}`);

    const shutdownPromises = this.handlers.map(async (handler) => {
      const timeout = handler.timeout || this.defaultTimeout;

      try {
        logger.info(`Shutting down: ${handler.name}`);

        // Create a timeout promise
        const timeoutPromise = new Promise<void>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Shutdown timeout for ${handler.name}`));
          }, timeout);
        });

        // Race between shutdown and timeout
        await Promise.race([handler.shutdown(), timeoutPromise]);

        logger.info(`Successfully shut down: ${handler.name}`);
      } catch (error) {
        logger.error(`Error shutting down ${handler.name}:`, error);
      }
    });

    try {
      // Wait for all handlers to complete or timeout
      await Promise.allSettled(shutdownPromises);
      logger.info("Graceful shutdown completed");
    } catch (error) {
      logger.error("Error during graceful shutdown:", error);
    } finally {
      // Force exit after cleanup
      process.exit(signal === "UNCAUGHT_EXCEPTION" || signal === "UNHANDLED_REJECTION" ? 1 : 0);
    }
  }

  /**
   * Get shutdown status
   */
  isShutdownInProgress(): boolean {
    return this.isShuttingDown;
  }
}

/**
 * Utility function to create a shutdown handler
 */
export function createShutdownHandler(
  name: string,
  shutdownFn: () => Promise<void>,
  timeout?: number,
): ShutdownHandler {
  return {
    name,
    shutdown: shutdownFn,
    timeout,
  };
}
