/**
 * API Request and Response Types
 *
 * These types define the structure of API requests and responses across all services.
 */

// --- Common API Types ---

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  timestamp: Date;
  requestId?: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string; // Only in development
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// --- Chat Service APIs ---

export interface LogMessageResponse {
  messageId: string;
  logged: boolean;
}

export interface GetConversationHistoryRequest extends PaginationParams {
  conversationId: string;
  fromDate?: Date;
  toDate?: Date;
  messageTypes?: string[];
}

export interface GetConversationsRequest extends PaginationParams {
  userId?: string;
  botId?: string;
  status?: "active" | "inactive" | "completed" | "abandoned";
  fromDate?: Date;
  toDate?: Date;
}

// --- Analytics APIs ---

export interface ConversationAnalytics {
  totalConversations: number;
  activeConversations: number;
  completedConversations: number;
  abandonedConversations: number;
  averageSessionDuration: number;
  averageMessagesPerConversation: number;
  topIntents: Array<{
    intent: string;
    count: number;
    percentage: number;
  }>;
  topFlows: Array<{
    flowId: string;
    flowName: string;
    count: number;
    completionRate: number;
  }>;
}

export interface GetAnalyticsRequest {
  botId?: string;
  fromDate: Date;
  toDate: Date;
  granularity?: "hour" | "day" | "week" | "month";
}

export interface GetAnalyticsResponse {
  analytics: ConversationAnalytics;
  timeSeries?: Array<{
    timestamp: Date;
    metrics: Partial<ConversationAnalytics>;
  }>;
}

// --- Health Check APIs ---

export interface HealthCheckResponse {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: Date;
  version: string;
  uptime: number;
  dependencies: Array<{
    name: string;
    status: "healthy" | "unhealthy";
    responseTime?: number;
    error?: string;
  }>;
}
