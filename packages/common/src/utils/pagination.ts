import { PaginatedResponse } from "../types/api.types";

export interface PaginationOptions {
  page?: number;
  limit?: number;
  total?: number;
}

export function createPaginatedResponse<T>(
  items: T[],
  options: PaginationOptions,
): PaginatedResponse<T> {
  const { page = 0, limit = 0, total = 0 } = options;
  const totalPages = Math.ceil(total / limit);

  return {
    items,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

export function getPaginationOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}
