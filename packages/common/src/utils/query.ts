import { Op, WhereOptions, FindOptions, ModelStatic, Model } from "sequelize";
import { PaginatedResult } from "../types/filter.types";
import { createPaginatedResponse, getPaginationOffset } from "./pagination";
import { FilterObject, FilterCondition, PaginationQuery } from "../schemas/common.schemas";
import { Models } from "@neuratalk/bot-store";

export function buildWhereClause(
  filter?: FilterObject,
  search?: string,
  searchFields: string[] = [],
): WhereOptions {
  const where: any = {};

  // Search functionality
  if (search && searchFields.length > 0) {
    where[Op.or] = searchFields.map((field) => ({
      [field]: { [Op.like]: `%${search}%` },
    }));
  }

  // Filter functionality
  if (filter) {
    Object.entries(filter).forEach(([column, conditions]: [string, FilterCondition]) => {
      if (conditions.eq !== undefined) where[column] = conditions.eq;
      if (conditions.ne !== undefined) where[column] = { [Op.ne]: conditions.ne };
      if (conditions.gt !== undefined) where[column] = { [Op.gt]: conditions.gt };
      if (conditions.gte !== undefined) where[column] = { [Op.gte]: conditions.gte };
      if (conditions.lt !== undefined) where[column] = { [Op.lt]: conditions.lt };
      if (conditions.lte !== undefined) where[column] = { [Op.lte]: conditions.lte };
      if (conditions.like !== undefined) where[column] = { [Op.like]: `%${conditions.like}%` };
      if (conditions.in !== undefined) where[column] = { [Op.in]: conditions.in };
      if (conditions.notIn !== undefined) where[column] = { [Op.notIn]: conditions.notIn };
      if (conditions.isNull === true) where[column] = { [Op.is]: null };
      if (conditions.isNotNull === true) where[column] = { [Op.not]: null };
    });
  }

  return where as WhereOptions;
}

export function parseIncludeQuery(includeQuery: string | undefined, models: Models): any[] {
  if (!includeQuery) return [];

  return includeQuery
    .split(",")
    .map((assoc: string) => {
      switch (assoc.trim()) {
        case "faqTranslations":
          return { model: models.FaqTranslation, as: "faqTranslations" };
        case "category":
          return { model: models.FaqCategory, as: "category" };
        case "intentUtterances":
          return {
            model: models.IntentUtterance,
            as: "intentUtterances",
            include: [{ model: models.IntentUtteranceTranslation, as: "utteranceTranslations" }],
          };
        case "utteranceTranslations":
          return { model: models.IntentUtteranceTranslation, as: "utteranceTranslations" };
        case "entities":
          return { model: models.Entities, as: "entities" };
        case "intentItem":
          return { model: models.IntentItems, as: "intentItem" };
        case "language":
          return { model: models.Language, as: "language" };
        case "bot":
          return { model: models.Bot, as: "bot" };
        default:
          return null;
      }
    })
    .filter(Boolean);
}

export async function getPaginatedResults<T extends Model>(
  model: ModelStatic<T>,
  query: PaginationQuery,
  searchFields: string[] = [],
  include?: any[],
): Promise<PaginatedResult<T>> {
  const { page = 0, limit = 0, search, filter, sortBy = "updatedAt", sortOrder = "desc" } = query;
  const offset = getPaginationOffset(page, limit);

  const where = buildWhereClause(filter, search, searchFields);

  const findOptions: FindOptions = {
    where,
    limit,
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]],
    ...(include && { include }),
  };

  const { count, rows } = await model.findAndCountAll(findOptions);

  return createPaginatedResponse(rows, { page, limit, total: count });
}
