/**
 * Shared Storage Service
 * Provides local file storage fallback for development
 */

import fs from "fs/promises";
import path from "path";
import AWS from "aws-sdk";

export interface StorageConfig {
  type: "s3" | "local";
  localPath?: string;
  s3Config?: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
    endpoint?: string;
  };
}

export class StorageService {
  private s3?: AWS.S3;
  private config: StorageConfig;

  constructor(config: StorageConfig) {
    this.config = config;
    
    if (config.type === "s3" && config.s3Config) {
      this.s3 = new AWS.S3({
        accessKeyId: config.s3Config.accessKeyId,
        secretAccessKey: config.s3Config.secretAccessKey,
        region: config.s3Config.region,
        endpoint: config.s3Config.endpoint,
        s3ForcePathStyle: !!config.s3Config.endpoint,
      });
    }
  }

  async uploadFile(filePath: string, key: string): Promise<string> {
    if (this.config.type === "s3" && this.s3 && this.config.s3Config) {
      return this.uploadToS3(filePath, key);
    } else {
      return this.uploadToLocal(filePath, key);
    }
  }

  async downloadFile(url: string, destinationPath: string): Promise<void> {
    if (this.config.type === "s3" && this.s3) {
      return this.downloadFromS3(url, destinationPath);
    } else {
      return this.downloadFromLocal(url, destinationPath);
    }
  }

  private async uploadToS3(filePath: string, key: string): Promise<string> {
    if (!this.s3 || !this.config.s3Config) {
      throw new Error("S3 not configured");
    }

    const fileStream = require("fs").createReadStream(filePath);
    
    const uploadParams = {
      Bucket: this.config.s3Config.bucket,
      Key: key,
      Body: fileStream,
    };

    const result = await this.s3.upload(uploadParams).promise();
    return result.Location;
  }

  private async uploadToLocal(filePath: string, key: string): Promise<string> {
    const localPath = this.config.localPath || "./storage";
    const destinationPath = path.join(localPath, key);
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(destinationPath), { recursive: true });
    
    // Copy file
    await fs.copyFile(filePath, destinationPath);
    
    return `file://${destinationPath}`;
  }

  private async downloadFromS3(url: string, destinationPath: string): Promise<void> {
    if (!this.s3 || !this.config.s3Config) {
      throw new Error("S3 not configured");
    }

    const key = this.extractKeyFromUrl(url);
    
    const downloadParams = {
      Bucket: this.config.s3Config.bucket,
      Key: key,
    };

    const result = await this.s3.getObject(downloadParams).promise();
    
    if (result.Body) {
      await fs.writeFile(destinationPath, result.Body as Buffer);
    }
  }

  private async downloadFromLocal(url: string, destinationPath: string): Promise<void> {
    const sourcePath = url.replace("file://", "");
    await fs.copyFile(sourcePath, destinationPath);
  }

  private extractKeyFromUrl(url: string): string {
    if (!this.config.s3Config) return url;
    
    const urlParts = url.split("/");
    const bucketIndex = urlParts.findIndex(part => part === this.config.s3Config!.bucket);
    
    if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {
      return urlParts.slice(bucketIndex + 1).join("/");
    }
    
    return url.split("/").pop() || url;
  }

  async healthCheck(): Promise<{ status: "healthy" | "unhealthy"; responseTime?: number }> {
    const start = Date.now();
    
    try {
      if (this.config.type === "s3" && this.s3 && this.config.s3Config) {
        await this.s3.headBucket({ Bucket: this.config.s3Config.bucket }).promise();
      } else {
        // Check local storage directory
        const localPath = this.config.localPath || "./storage";
        await fs.access(localPath);
      }
      
      const responseTime = Date.now() - start;
      return { status: "healthy", responseTime };
    } catch (error) {
      return { status: "unhealthy" };
    }
  }
}