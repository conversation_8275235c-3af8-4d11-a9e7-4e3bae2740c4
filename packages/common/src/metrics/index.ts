/**
 * Shared KPI Metrics
 */

import {
  register,
  collectDefaultMetrics,
  Counter,
  Histogram,
  Gauge,
} from "prom-client";

// Collect default metrics
collectDefaultMetrics();

// API Metrics
export const apiRequestsTotal = new Counter({
  name: "api_requests_total",
  help: "Total number of API requests",
  labelNames: ["method", "route", "status_code", "service"],
});

export const apiRequestDuration = new Histogram({
  name: "api_request_duration_seconds",
  help: "API request duration in seconds",
  labelNames: ["method", "route", "service"],
  buckets: [0.1, 0.5, 1, 2, 5, 10],
});

// Chat Gateway Metrics
export const activeWebSocketConnections = new Gauge({
  name: "websocket_connections_active",
  help: "Number of active WebSocket connections",
});

export const messagesProcessedTotal = new Counter({
  name: "messages_processed_total",
  help: "Total number of messages processed",
  labelNames: ["channel", "type", "service"],
});

export const conversationsActive = new Gauge({
  name: "conversations_active",
  help: "Number of active conversations",
  labelNames: ["channel"],
});

// Rasa Server Metrics
export const modelsLoaded = new Gauge({
  name: "rasa_models_loaded",
  help: "Number of loaded Rasa models",
});

export const modelLoadDuration = new Histogram({
  name: "rasa_model_load_duration_seconds",
  help: "Time taken to load a Rasa model",
  buckets: [1, 5, 10, 30, 60, 120],
});

export const nluRequestsTotal = new Counter({
  name: "nlu_requests_total",
  help: "Total number of NLU requests",
  labelNames: ["bot_id", "intent"],
});

// Training Server Metrics
export const trainingJobsTotal = new Counter({
  name: "training_jobs_total",
  help: "Total number of training jobs",
  labelNames: ["status"],
});

export const trainingDuration = new Histogram({
  name: "training_duration_seconds",
  help: "Training job duration in seconds",
  buckets: [60, 300, 600, 1200, 1800, 3600],
});

export const activeTrainingJobs = new Gauge({
  name: "training_jobs_active",
  help: "Number of active training jobs",
});

// Utility functions
export const startTimer = (histogram: Histogram<string>) => {
  return histogram.startTimer();
};

export const incrementCounter = (
  counter: Counter<string>,
  labels?: Record<string, string | number>
) => {
  counter.inc(labels ?? {});//TODO: rm {}
};

export const setGauge = (
  gauge: Gauge<string>,
  value: number,
  labels?: Record<string, string | number>
) => {
  gauge.set(labels ?? {}, value);//TODO: rmm {}
};

export const getMetrics = () => register.metrics();

export { register };