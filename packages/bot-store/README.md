# @neuratalk/bot-store

Shared database layer for bot services containing models, connections, and database utilities.

## Features

- Shared Sequelize models (Bot, Flow, BotModel, ConversationContext)
- Database connection management
- Environment-specific configurations
- Health check utilities

## Usage

```typescript
import { DatabaseConnection, BotModel, FlowModel } from "@neuratalk/bot-store";

const db = new DatabaseConnection("development");
await db.connect();

// Access models
const bots = await db.models.Bot.findAll();
```

## Models

- **Bot**: Bot configuration and metadata
- **Flow**: Conversation flows
- **BotModel**: Trained model versions
- **ConversationContext**: User conversation state
