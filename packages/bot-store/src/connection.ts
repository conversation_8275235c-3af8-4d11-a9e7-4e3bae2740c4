import { Sequelize } from "sequelize";
import { dbConfig } from "./config";
import { Models, initModels } from "./models";
import { logger } from "@neuratalk/common";

// Create a default sequelize instance for model definitions
const config = dbConfig.development;
export const sequelize = new Sequelize({
  dialect: config.dialect,
  host: config.host,
  port: config.port,
  database: config.database,
  username: config.username,
  password: config.password,
  pool: config.pool,
  logging: config.logging,
});

export class DatabaseConnection {
  private sequelize: Sequelize;
  public models!: Models;

  constructor(environment: "development" | "test" | "production" = "development") {
    const config = dbConfig[environment];

    this.sequelize = new Sequelize({
      dialect: config.dialect,
      host: config.host,
      port: config.port,
      database: config.database,
      username: config.username,
      password: config.password,
      pool: config.pool,
      logging: config.logging,
    });
  }

  async connect(): Promise<void> {
    try {
      await this.sequelize.authenticate();
      this.models = initModels(this.sequelize);
      logger.info("Database connection established successfully");
    } catch (error) {
      logger.error("Failed to connect to database:", error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.sequelize.close();
      logger.info("Database connection closed");
    } catch (error) {
      logger.error("Error closing database connection:", error);
    }
  }

  getSequelize(): Sequelize {
    return this.sequelize;
  }

  async transaction<T>(callback: (transaction: any) => Promise<T>): Promise<T> {
    return await this.sequelize.transaction(callback);
  }

  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    responseTime?: number;
  }> {
    const start = Date.now();

    try {
      await this.sequelize.authenticate();
      const responseTime = Date.now() - start;

      return {
        status: "healthy",
        responseTime,
      };
    } catch (error) {
      logger.error("Database health check failed:", error);
      return {
        status: "unhealthy",
      };
    }
  }
}
