import { DataTypes, Model, Sequelize, BelongsTo, BelongsToGetAssociationMixin } from "sequelize";
import { BotModel } from "./bot.model";

export enum BotModelStatus {
  TRAINING = "training",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
}

export interface BotModelAttributes {
  id: string;
  botId: string;
  version: number;
  status: BotModelStatus;
  path?: string;
  rasaModelPath?: string;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;

  // Associations
  bot?: BotModel;
}

export class BotModelModel extends Model<BotModelAttributes> implements BotModelAttributes {
  public id!: string;
  public botId!: string;
  public version!: number;
  public status!: BotModelStatus;
  public path?: string;
  public rasaModelPath?: string;
  public metadata?: Record<string, any>;
  public readonly createdAt?: Date;
  public readonly updatedAt?: Date;

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;

  // Properties for eager loading
  public bot?: BotModel;

  public static associations: {
    bot: BelongsTo;
  };
}

export function initBotModelModel(sequelize: Sequelize): typeof BotModelModel {
  BotModelModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      version: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(BotModelStatus.TRAINING, BotModelStatus.SUCCEEDED, BotModelStatus.FAILED),
        allowNull: false,
        defaultValue: BotModelStatus.TRAINING,
      },
      path: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      rasaModelPath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
      },
      updatedAt: {
        type: DataTypes.DATE,
      },
    },
    {
      sequelize,
      tableName: "bot_models",
      timestamps: true,
      paranoid: true,
    }
  );

  return BotModelModel;
}