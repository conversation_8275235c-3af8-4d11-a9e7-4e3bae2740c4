/**
 * Message Service
 */

import axios from "axios";
import { IncomingMessage, OutgoingMessage } from "@neuratalk/common";
import config from "../config";
import logger from "../utils/logger";
import { RedisService } from "./redis.service";
import { messagesProcessedTotal } from "@neuratalk/common";

export class MessageService {
  constructor(private redisService: RedisService) {}

  async processIncomingMessage(
    conversationId: string,
    message: IncomingMessage,
    channelType?: string
  ): Promise<OutgoingMessage[]> {
    try {
      // Increment metrics
      messagesProcessedTotal.inc({
        channel: channelType || "unknown",
        type: message.messageType || "text",
        service: "chat-gateway",
      });

      // Check if conversation is in agent mode
      const agentMode = await this.redisService.getAgentMode(conversationId);

      if (agentMode?.isAgentMode) {
        // Forward to agent portal
        return await this.forwardToAgent(
          conversationId,
          message,
          agentMode.agentId
        );
      } else {
        // Forward to bot interaction service
        return await this.forwardToBot(conversationId, message);
      }
    } catch (error) {
      logger.error("Error processing incoming message:", error);
      throw error;
    }
  }

  private async forwardToAgent(
    conversationId: string,
    message: IncomingMessage,
    agentId?: string
  ): Promise<OutgoingMessage[]> {
    try {
      const response = await axios.post(
        `${config.services.agentPortalUrl}/api/v1/conversations/${conversationId}/messages`,
        {
          ...message,
          agentId,
        },
        {
          headers: {
            Authorization: `Bearer ${config.security.apiKey}`,
            "Content-Type": "application/json",
          },
          timeout: 10000,
        }
      );

      return response.data.data?.messages || [];
    } catch (error) {
      logger.error("Error forwarding message to agent:", error);

      // Fallback to bot if agent service is unavailable
      logger.warn("Falling back to bot interaction service");
      return await this.forwardToBot(conversationId, message);
    }
  }

  private async forwardToBot(
    conversationId: string,
    message: IncomingMessage
  ): Promise<OutgoingMessage[]> {
    try {
      const response = await axios.post(
        `${config.services.botInteractionUrl}/api/v1/conversations/${conversationId}/messages`,
        message,
        {
          headers: {
            Authorization: `Bearer ${config.security.apiKey}`,
            "Content-Type": "application/json",
          },
          timeout: 15000,
        }
      );

      return response.data.data?.response || [];
    } catch (error) {
      logger.error("Error forwarding message to bot:", error);

      // Return error message
      return [
        {
          content:
            "Sorry, I'm having trouble processing your message right now. Please try again later.",
          messageType: "text",
        },
      ];
    }
  }

  async logMessage(conversationId: string, message: any): Promise<void> {
    try {
      await axios.post(
        `${config.services.chatServiceUrl}/api/v1/messages/log`,
        {
          message: {
            ...message,
            conversationId,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${config.security.apiKey}`,
            "Content-Type": "application/json",
          },
          timeout: 5000,
        }
      );
    } catch (error) {
      logger.error("Error logging message:", error);
      // Don't throw error as logging is not critical
    }
  }

  async getChannelConfig(botId: string, channelType: string): Promise<any> {
    try {
      const response = await axios.get(
        `${config.services.botBuilderUrl}/api/v1/bots/${botId}/channels/${channelType}`,
        {
          headers: {
            Authorization: `Bearer ${config.security.apiKey}`,
          },
          timeout: 5000,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(`Error fetching channel config for bot ${botId}:`, error);
      return null;
    }
  }

  async switchToAgent(conversationId: string, agentId: string): Promise<void> {
    await this.redisService.setAgentMode(conversationId, true, agentId);
    logger.info(
      `Conversation ${conversationId} switched to agent mode with agent ${agentId}`
    );
  }

  async switchToBot(conversationId: string): Promise<void> {
    await this.redisService.setAgentMode(conversationId, false);
    logger.info(`Conversation ${conversationId} switched to bot mode`);
  }
}
