/**
 * Redis Service
 */

import Redis from 'ioredis';
import config from '../config';
import logger from '../utils/logger';

export class RedisService {
  private client: Redis;

  constructor() {
    this.client = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
    } as any);

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      logger.info('Redis client connected');
    });

    this.client.on('error', (err) => {
      logger.error('Redis client error:', err);
    });

    this.client.on('close', () => {
      logger.warn('Redis connection closed');
    });
  }

  async setAgentMode(conversationId: string, isAgentMode: boolean, agentId?: string): Promise<void> {
    const key = `conversation:${conversationId}:agent_mode`;
    const value = JSON.stringify({ isAgentMode, agentId, timestamp: Date.now() });
    await this.client.set(key, value, 'EX', 3600); // 1 hour expiry
  }

  async getAgentMode(conversationId: string): Promise<{ isAgentMode: boolean; agentId?: string } | null> {
    const key = `conversation:${conversationId}:agent_mode`;
    const value = await this.client.get(key);
    
    if (!value) return null;
    
    try {
      const parsed = JSON.parse(value);
      return { isAgentMode: parsed.isAgentMode, agentId: parsed.agentId };
    } catch (error) {
      logger.error('Error parsing agent mode data:', error);
      return null;
    }
  }

  async setConversationContext(conversationId: string, context: Record<string, any>): Promise<void> {
    const key = `conversation:${conversationId}:context`;
    await this.client.set(key, JSON.stringify(context), 'EX', 7200); // 2 hours expiry
  }

  async getConversationContext(conversationId: string): Promise<Record<string, any> | null> {
    const key = `conversation:${conversationId}:context`;
    const value = await this.client.get(key);
    
    if (!value) return null;
    
    try {
      return JSON.parse(value);
    } catch (error) {
      logger.error('Error parsing conversation context:', error);
      return null;
    }
  }

  async setActiveSocket(conversationId: string, socketId: string): Promise<void> {
    const key = `conversation:${conversationId}:socket`;
    await this.client.set(key, socketId, 'EX', 3600); // 1 hour expiry
  }

  async getActiveSocket(conversationId: string): Promise<string | null> {
    const key = `conversation:${conversationId}:socket`;
    return await this.client.get(key);
  }

  async removeActiveSocket(conversationId: string): Promise<void> {
    const key = `conversation:${conversationId}:socket`;
    await this.client.del(key);
  }

  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; responseTime?: number }> {
    const start = Date.now();
    
    try {
      await this.client.ping();
      const responseTime = Date.now() - start;
      
      return {
        status: 'healthy',
        responseTime,
      };
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return {
        status: 'unhealthy',
      };
    }
  }

  async disconnect(): Promise<void> {
    await this.client.quit();
  }
}