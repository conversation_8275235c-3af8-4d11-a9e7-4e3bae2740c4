/**
 * Chat Gateway Service Application
 */

import express, { Application, Request, Response, NextFunction } from "express";
import { createServer, Server as HttpServer } from "http";
import path from "path";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";

import { RedisService } from "./services/redis.service";
import { MessageService } from "./services/message.service";
import { WebSocketService } from "./websocket/websocket.service";
import { WhatsAppChannel } from "./channels/whatsapp.channel";
import { GatewayController } from "./controllers/gateway.controller";
import { routes, RouteHandlers } from "./routes";
import {
  apiRequestsTotal,
  apiRequestDuration,
  startTimer,
} from "@neuratalk/common";

import config from "./config";
import logger from "./utils/logger";
import { ApiResponse } from "@neuratalk/common";

export class App {
  public app: Application;
  public server: HttpServer;
  private redisService!: RedisService;
  private messageService!: MessageService;
  private websocketService!: WebSocketService;
  private whatsappChannel!: WhatsAppChannel;
  private gatewayController!: GatewayController;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.initializeServices();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeServices(): void {
    this.redisService = new RedisService();
    this.messageService = new MessageService(this.redisService);
    this.websocketService = new WebSocketService(
      this.server,
      this.messageService,
      this.redisService
    );
    this.whatsappChannel = new WhatsAppChannel(this.messageService);
    this.gatewayController = new GatewayController(
      this.messageService,
      this.whatsappChannel
    );
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // CORS configuration
    this.app.use(
      cors({
        origin: config.server.corsOrigins,
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization"],
      })
    );

    // Compression
    this.app.use(compression() as any);

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        })
      );
    }

    // Request ID and metrics middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);

      // Start timer for metrics
      const timer = startTimer(apiRequestDuration);

      res.on("finish", () => {
        timer();
        apiRequestsTotal.inc({
          method: req.method,
          route: req.route?.path || req.path,
          status_code: res.statusCode,
          service: "chat-gateway",
        });
      });

      next();
    });
  }

  private initializeRoutes(): void {
    // Create route handlers map
    const handlers: RouteHandlers = {
      health: async (req: Request, res: Response) => {
        const redisHealth = await this.redisService.healthCheck();
        const isHealthy = redisHealth.status === "healthy";

        res.status(isHealthy ? 200 : 503).json({
          success: isHealthy,
          data: {
            status: isHealthy ? "healthy" : "unhealthy",
            service: "chat-gateway",
            version: process.env.npm_package_version || "1.0.0",
            timestamp: new Date(),
            dependencies: [{ name: "redis", ...redisHealth }],
            websocketClients: this.websocketService.getConnectedClients(),
          },
          timestamp: new Date(),
        } as ApiResponse);
      },
      root: (req: Request, res: Response) => {
        res.json({
          success: true,
          data: {
            service: "chat-gateway",
            version: process.env.npm_package_version || "1.0.0",
            environment: config.server.env,
            timestamp: new Date(),
          },
          timestamp: new Date(),
        } as ApiResponse);
      },
      switchToAgent: (req, res) =>
        this.gatewayController.switchToAgent(req, res),
      switchToBot: (req, res) => this.gatewayController.switchToBot(req, res),
      getConversationStatus: (req, res) =>
        this.gatewayController.getConversationStatus(req, res),
      handleWhatsAppWebhook: (req, res) =>
        this.gatewayController.handleWhatsAppWebhook(req, res),
      handleTelegramWebhook: (req, res) =>
        this.gatewayController.handleTelegramWebhook(req, res),
      handleSlackWebhook: (req, res) =>
        this.gatewayController.handleSlackWebhook(req, res),
    };

    // Register routes dynamically
    routes.forEach((route) => {
      const handler = handlers[route.handler];
      if (!handler) {
        logger.error(`Handler ${route.handler} not found`);
        return;
      }

      this.app[route.method](route.path, handler);
    });

    // Swagger documentation
    this.app.use(
      "/api-docs",
      swaggerUi.serve as any,
      swaggerUi.setup(swaggerSpec) as any
    );
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Serve web client
    this.app.use(
      "/client",
      express.static(path.join(__dirname, "../../web-client"))
    );
    this.app.get("/client", (req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, "../../web-client/index.html"));
    });

    // Metrics endpoint
    this.app.get("/metrics", async (req: Request, res: Response) => {
      const { getMetrics } = await import("@neuratalk/common");
      res.set("Content-Type", "text/plain");
      res.end(await getMetrics());
    });

    // 404 handler
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use(
      (error: Error, req: Request, res: Response, next: NextFunction) => {
        logger.error("Unhandled error:", {
          error: error.message,
          stack: error.stack,
          requestId: (req as any).requestId,
          method: req.method,
          path: req.path,
        });

        const message =
          config.server.env === "production"
            ? "An internal error occurred"
            : error.message;

        res.status(500).json({
          success: false,
          error: {
            code: "INTERNAL_ERROR",
            message,
            ...(config.server.env !== "production" && { stack: error.stack }),
          },
          timestamp: new Date(),
        } as ApiResponse);
      }
    );
  }

  public async start(): Promise<void> {
    try {
      // Start server
      const port = config.server.port;
      this.server.listen(port, () => {
        logger.info(`Chat Gateway Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
        logger.info(`Metrics: http://localhost:${port}/metrics`);
        logger.info(`WebSocket server ready`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Chat Gateway Service...");
      await this.redisService.disconnect();
      this.server.close();
      logger.info("Chat Gateway Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
