/**
 * WebSocket Service
 */

import { Server as SocketIOServer, Socket } from "socket.io";
import { Server as HttpServer } from "http";
import { IncomingMessage, OutgoingMessage } from "@neuratalk/common";
import config from "../config";
import logger from "../utils/logger";
import { MessageService } from "../services/message.service";
import { RedisService } from "../services/redis.service";

export class WebSocketService {
  private io: SocketIOServer;

  constructor(
    server: HttpServer,
    private messageService: MessageService,
    private redisService: RedisService
  ) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: config.server.corsOrigins,
        methods: ["GET", "POST"],
        credentials: true,
      },
      pingTimeout: config.websocket.connectionTimeout,
      pingInterval: config.websocket.heartbeatInterval,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.io.on("connection", (socket: Socket) => {
      logger.info(`WebSocket client connected: ${socket.id}`);

      socket.on(
        "join_conversation",
        async (data: { conversationId: string }) => {
          try {
            const { conversationId } = data;

            // Join conversation room
            await socket.join(conversationId);

            // Store active socket for this conversation
            await this.redisService.setActiveSocket(conversationId, socket.id);

            logger.info(
              `Socket ${socket.id} joined conversation ${conversationId}`
            );

            socket.emit("joined_conversation", { conversationId });
          } catch (error) {
            logger.error("Error joining conversation:", error);
            socket.emit("error", { message: "Failed to join conversation" });
          }
        }
      );

      socket.on(
        "send_message",
        async (data: IncomingMessage & { conversationId: string }) => {
          try {
            const { conversationId, ...message } = data;

            logger.info(
              `Received message from ${socket.id} for conversation ${conversationId}`
            );

            // Process the message
            const responses = await this.messageService.processIncomingMessage(
              conversationId,
              { conversationId, ...message }
            );

            // Log the incoming message
            await this.messageService.logMessage(conversationId, {
              ...message,
              sender: "user",
              timestamp: new Date(),
            });

            // Send responses back to the client
            for (const response of responses) {
              socket.emit("message_response", {
                conversationId,
                ...response,
                timestamp: new Date(),
              });

              // Log the outgoing message
              await this.messageService.logMessage(conversationId, {
                ...response,
                sender: "bot",
                timestamp: new Date(),
              });
            }
          } catch (error) {
            logger.error("Error processing message:", error);
            socket.emit("error", { message: "Failed to process message" });
          }
        }
      );

      socket.on("typing_start", (data: { conversationId: string }) => {
        socket.to(data.conversationId).emit("typing_indicator", {
          conversationId: data.conversationId,
          isTyping: true,
        });
      });

      socket.on("typing_stop", (data: { conversationId: string }) => {
        socket.to(data.conversationId).emit("typing_indicator", {
          conversationId: data.conversationId,
          isTyping: false,
        });
      });

      socket.on("disconnect", async () => {
        logger.info(`WebSocket client disconnected: ${socket.id}`);

        // Clean up active socket references
        // Note: In a production environment, you might want to store conversation-socket mappings
        // to clean up more efficiently
      });
    });
  }

  async sendMessageToConversation(
    conversationId: string,
    message: OutgoingMessage
  ): Promise<void> {
    this.io.to(conversationId).emit("message_response", {
      conversationId,
      ...message,
      timestamp: new Date(),
    });
  }

  async notifyAgentSwitch(
    conversationId: string,
    agentId: string
  ): Promise<void> {
    this.io.to(conversationId).emit("agent_joined", {
      conversationId,
      agentId,
      timestamp: new Date(),
    });
  }

  async notifyBotSwitch(conversationId: string): Promise<void> {
    this.io.to(conversationId).emit("bot_resumed", {
      conversationId,
      timestamp: new Date(),
    });
  }

  getConnectedClients(): number {
    return this.io.engine.clientsCount;
  }
}
