/**
 * Gateway Controller
 */

import { Request, Response } from "express";
import { ApiResponse } from "@neuratalk/common";
import { MessageService } from "../services/message.service";
import { WhatsAppChannel } from "../channels/whatsapp.channel";
import logger from "../utils/logger";

export class GatewayController {
  constructor(
    private messageService: MessageService,
    private whatsappChannel: WhatsAppChannel
  ) {}

  /**
   * @swagger
   * /api/v1/conversations/{conversationId}/switch-to-agent:
   *   post:
   *     summary: Switch conversation to agent mode
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               agentId:
   *                 type: string
   *     responses:
   *       200:
   *         description: Successfully switched to agent
   */

  async switchToAgent(req: Request, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;
      const { agentId } = req.body;

      if (!agentId) {
        res.status(400).json({
          success: false,
          error: {
            code: "MISSING_AGENT_ID",
            message: "Agent ID is required",
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }

      await this.messageService.switchToAgent(conversationId, agentId);

      res.json({
        success: true,
        data: {
          conversationId,
          agentId,
          switched: true,
        },
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error switching to agent:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "SWITCH_AGENT_ERROR",
          message: "Failed to switch to agent",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /api/v1/conversations/{conversationId}/switch-to-bot:
   *   post:
   *     summary: Switch conversation to bot mode
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Successfully switched to bot
   */
  async switchToBot(req: Request, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;

      await this.messageService.switchToBot(conversationId);

      res.json({
        success: true,
        data: {
          conversationId,
          switched: true,
        },
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error switching to bot:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "SWITCH_BOT_ERROR",
          message: "Failed to switch to bot",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }

  /**
   * @swagger
   * /webhooks/whatsapp:
   *   post:
   *     summary: Handle WhatsApp webhook
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Webhook processed successfully
   */
  async handleWhatsAppWebhook(req: Request, res: Response): Promise<void> {
    await this.whatsappChannel.handleWebhook(req, res);
  }

  async handleTelegramWebhook(req: Request, res: Response): Promise<void> {
    try {
      // Implement Telegram webhook handling
      logger.info("Telegram webhook received:", req.body);
      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Telegram webhook error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }

  async handleSlackWebhook(req: Request, res: Response): Promise<void> {
    try {
      // Implement Slack webhook handling
      logger.info("Slack webhook received:", req.body);
      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("Slack webhook error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }

  //TODO: not needed
  async getConversationStatus(req: Request, res: Response): Promise<void> {
    try {
      const { conversationId } = req.params;

      // This would typically fetch from database and Redis
      res.json({
        success: true,
        data: {
          conversationId,
          status: "active",
          isAgentMode: false,
        },
        timestamp: new Date(),
      } as ApiResponse);
    } catch (error) {
      logger.error("Error getting conversation status:", error);
      res.status(500).json({
        success: false,
        error: {
          code: "GET_STATUS_ERROR",
          message: "Failed to get conversation status",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  }
}
