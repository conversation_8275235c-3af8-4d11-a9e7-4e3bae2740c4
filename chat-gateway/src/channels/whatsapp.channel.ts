/**
 * WhatsApp Channel Handler
 */

import { Request, Response } from "express";
import { IncomingMessage, OutgoingMessage } from "@neuratalk/common";
import config from "../config";
import logger from "../utils/logger";
import { MessageService } from "../services/message.service";
import { ConversationModel } from "../models/conversation.model";

export class WhatsAppChannel {
  constructor(private messageService: MessageService) {}

  async handleWebhook(req: Request, res: Response): Promise<void> {
    try {
      const { body } = req;

      // Verify webhook (simplified - implement proper verification in production)
      if (
        req.query["hub.verify_token"] === config.channels.whatsapp.webhookToken
      ) {
        res.status(200).send(req.query["hub.challenge"]);
        return;
      }

      // Process incoming message
      if (
        body.entry &&
        body.entry[0].changes &&
        body.entry[0].changes[0].value.messages
      ) {
        const message = body.entry[0].changes[0].value.messages[0];
        const from = message.from;
        const text = message.text?.body || "";

        if (text) {
          await this.processMessage(from, text);
        }
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error("WhatsApp webhook error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }

  private async processMessage(
    phoneNumber: string,
    text: string
  ): Promise<void> {
    try {
      // Find or create conversation
      const conversationId = await this.getOrCreateConversation(phoneNumber);

      const incomingMessage: IncomingMessage = {
        conversationId,
        content: text,
        messageType: "text",
        timestamp: new Date(),
      };

      // Process message
      const responses = await this.messageService.processIncomingMessage(
        conversationId,
        incomingMessage
      );

      // Send responses back via WhatsApp API
      for (const response of responses) {
        await this.sendWhatsAppMessage(phoneNumber, response);
      }
    } catch (error) {
      logger.error("Error processing WhatsApp message:", error);
    }
  }

  private async getOrCreateConversation(phoneNumber: string): Promise<string> {
    // This is a simplified implementation
    // In production, you'd need to handle bot selection, channel integration lookup, etc.

    let conversation = await ConversationModel.findOne({
      where: {
        channelType: "whatsapp",
        channelUserId: phoneNumber,
        status: "active",
      },
    });

    if (!conversation) {
      conversation = await ConversationModel.create({
        channelType: "whatsapp",
        channelUserId: phoneNumber,
        botId: "default-bot-id", // This should come from channel integration
        channelId: "whatsapp-channel-id", // This should come from channel integration
        status: "active",
        isAgentMode: false,
        startedAt: new Date(),
        lastMessageAt: new Date(),
      } as any);
    }

    return conversation.id;
  }

  private async sendWhatsAppMessage(
    phoneNumber: string,
    message: OutgoingMessage
  ): Promise<void> {
    try {
      // Implement WhatsApp Business API call here
      // This is a placeholder - you'll need to integrate with actual WhatsApp Business API
      logger.info(
        `Sending WhatsApp message to ${phoneNumber}:`,
        message.content
      );

      // Example API call structure:
      // await axios.post('https://graph.facebook.com/v17.0/YOUR_PHONE_NUMBER_ID/messages', {
      //   messaging_product: 'whatsapp',
      //   to: phoneNumber,
      //   text: { body: message.content }
      // }, {
      //   headers: {
      //     'Authorization': `Bearer ${WHATSAPP_ACCESS_TOKEN}`,
      //     'Content-Type': 'application/json'
      //   }
      // });
    } catch (error) {
      logger.error("Error sending WhatsApp message:", error);
    }
  }
}
